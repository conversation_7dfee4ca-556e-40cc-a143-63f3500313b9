import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/pi';

interface DeckCodeProps {
  code: string;
}

const DeckCode: React.FC<DeckCodeProps> = ({ code }) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="my-4 relative w-full">
      <div className="bg-gray-800 text-white p-4 rounded-md overflow-hidden">
        <pre className="font-mono text-sm whitespace-pre-wrap break-all">{code}</pre>
      </div>
      <button
        onClick={copyToClipboard}
        className="absolute top-2 right-2 p-2 rounded-md bg-gray-700 text-white hover:bg-gray-600 transition-colors"
        title="Copy deck code"
      >
        {copied ? <PiCheck className="text-green-400" /> : <PiCopy />}
      </button>
      <div className="mt-2 text-center">
        <span className="text-xs text-gray-500">
          Copy this code to import the deck directly into Hearthstone
        </span>
      </div>
    </div>
  );
};

export default DeckCode;
