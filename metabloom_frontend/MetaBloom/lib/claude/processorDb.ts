/**
 * Processor Database Access Functions
 *
 * This module provides database access functions for the Processor component.
 * It's responsible for:
 * 1. Executing queries against different database types
 * 2. Handling errors and retries
 * 3. Analyzing data sufficiency
 * 4. Generating additional queries based on initial results
 * 5. Using getInstructionKit() for dynamic schema lookup
 */

import {
  ExecutableQuery,
  ConditionalQuery,
  DataSufficiencyCheck,
  QueryResult
} from '@/lib/types/queryTypes';
import { logProcessor, logError, logDatabaseOperation } from '@/lib/utils/claudeLogger';


async function convertTextToEmbedding(text: string): Promise<number[]> {
  try {
    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: 'text-embedding-3-small',
        input: text.substring(0, 8000), // Truncate to token limit
        encoding_format: 'float'
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data[0].embedding;
  } catch (error) {
    logError('Processor', 'Failed to convert text to embedding', error);
    throw error;
  }
}
/**
 * Fallback to simple text search (no embeddings)
 */
function buildSimpleTextSearch(searchTerm: string, limit: number): { actualQuery: string; requestBody: any } {
  const actualQuery = `
    SELECT c.card_id, c.name, c.text, c.mana_cost,
           ts_rank(to_tsvector('english', c.name || ' ' || c.text), plainto_tsquery('english', $1)) as relevance
    FROM cards c
    WHERE to_tsvector('english', c.name || ' ' || c.text) @@ plainto_tsquery('english', $1)
    ORDER BY relevance DESC
    LIMIT $2
  `;

  return {
    actualQuery,
    requestBody: {
      query_type: 'postgres',
      query: actualQuery,
      params: [searchTerm, limit]
    }
  };
}

/**
 * Retrieve an instruction kit from the database
 *
 * @param kitId The instruction kit ID to retrieve
 * @returns The instruction kit content as JSON
 */
export async function getInstructionKit(kitId: string): Promise<any> {
  try {
    logProcessor(`Retrieving instruction kit: ${kitId}`);

    // Query the instruction_kits table in the postgres database
    const response = await fetch('https://wa6kt26wi1.execute-api.us-east-1.amazonaws.com/prod/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query_type: 'postgres',
        query: `SELECT content FROM instruction_kits WHERE kit_id = '${kitId}'`
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to retrieve instruction kit: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result || !Array.isArray(result) || result.length === 0) {
      throw new Error(`Instruction kit not found: ${kitId}`);
    }

    const content = result[0].content;
    logProcessor(`Successfully retrieved instruction kit: ${kitId}`, { content });

    return content;
  } catch (error) {
    logError('Processor', `Error retrieving instruction kit ${kitId}`, error);
    throw error;
  }
}

/**
 * Execute a single query against the appropriate database
 *
 * @param query The query to execute
 * @param apiConfig API configuration
 * @returns The query result
 */
export async function executeQuery(
  query: ExecutableQuery,
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<QueryResult> {
  const startTime = Date.now();

  try {
    // Log detailed information about the query being executed
    logProcessor(`Executing ${query.database} query: ${query.id}`, {
      query_id: query.id,
      database: query.database,
      purpose: query.purpose,
      priority: query.priority,
      query_text: query.query,
      params: query.params
    });

    // Log the database operation start
    logDatabaseOperation(query.database, `Starting query execution: ${query.id}`, {
      query_text: query.query,
      params: query.params,
      purpose: query.purpose,
      priority: query.priority
    });

    let data;

    // Execute the query against the appropriate database
    switch (query.database) {
      case 'postgres':
        data = await executePostgresQuery(query, apiConfig);
        break;

      case 'vector':
        data = await executeVectorQuery(query, apiConfig);
        break;

      case 'graphql':
        data = await executeGraphQLQuery(query, apiConfig);
        break;

      default:
        throw new Error(`Unsupported database type: ${query.database}`);
    }

    const executionTime = Date.now() - startTime;
    const rowCount = Array.isArray(data) ? data.length : (data && typeof data === 'object' ? 1 : 0);

    // Create the query result
    const result: QueryResult = {
      success: true,
      data,
      executionTime,
      rowCount
    };

    // Log detailed information about the query result
    logProcessor(`Query ${query.id} executed successfully`, {
      query_id: query.id,
      execution_time_ms: executionTime,
      row_count: rowCount,
      data_sample: Array.isArray(data) && data.length > 0 ? data.slice(0, 2) : data
    });

    // Log the database operation completion
    logDatabaseOperation(query.database, `Completed query execution: ${query.id}`, {
      execution_time_ms: executionTime,
      row_count: rowCount
    }, {
      data_sample: Array.isArray(data) && data.length > 0 ? data.slice(0, 2) : data
    });

    // 🔍 DEBUG: Log what the database actually returned
    console.log('🔍 DATABASE RETURNED:', JSON.stringify(data, null, 2));

    return result;
  } catch (error: any) {
    const executionTime = Date.now() - startTime;

    // Log detailed error information
    logError('Processor', `Error executing query ${query.id}`, error);

    // Log the database operation failure
    logDatabaseOperation(query.database, `Failed query execution: ${query.id}`, {
      query_text: query.query,
      params: query.params,
      execution_time_ms: executionTime,
      error_message: error.message || 'Unknown error'
    });

    // Create an error result
    const errorResult: QueryResult = {
      success: false,
      data: null,
      error: error.message || 'Unknown error',
      executionTime
    };

    return errorResult;
  }
}

/**
 * Execute a PostgreSQL query using instruction kits for schema validation
 */
async function executePostgresQuery(
  query: ExecutableQuery,
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<any> {
  try {
    // Build the actual query (server-side only)
    const { actualQuery, requestBody } = await buildQueryFromSpecification(query, apiConfig);

    // PII-Safe database request logging
    logProcessor('PostgreSQL API Request (PII-Safe)', {
      endpoint: apiConfig.endpoint,
      query_type: requestBody.query_type,
      query_preview: actualQuery.toString().slice(0, 200) + (actualQuery.toString().length > 200 ? '...' : ''),
      query_length: actualQuery.toString().length,
      params_count: requestBody.params?.length || 0,
      has_params: !!requestBody.params,
      query_id: query.id,
      timestamp: new Date().toISOString()
    });

    // Log what we're actually executing (not the specification)
    logProcessor(`Executing PostgreSQL query: ${actualQuery}`);

    // Execute the built query
    const response = await fetch(apiConfig.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.text();

      // Enhanced database error logging
      logError('Processor', 'PostgreSQL API request failed', {
        status: response.status,
        statusText: response.statusText,
        errorData: errorData,
        query_id: query.id,
        endpoint: apiConfig.endpoint,
        query_preview: actualQuery.toString().slice(0, 200),
        timestamp: new Date().toISOString()
      });

      throw new Error(`PostgreSQL API Error (${response.status}): ${errorData}`);
    }

    const result = await response.json();
    return result;

  } catch (error) {
    logError('Processor', `PostgreSQL query execution failed for ${query.id}`, error);
    throw error;
  }
}

/**
 * Execute a vector database query using instruction kits for method validation
 */
async function executeVectorQuery(
  query: ExecutableQuery,
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<any> {
  try {
    // Build the actual query (server-side only)
    const { actualQuery, requestBody } = await buildQueryFromSpecification(query, apiConfig);

    // PII-Safe database request logging
    logProcessor('Vector API Request (PII-Safe)', {
      endpoint: apiConfig.endpoint,
      query_type: requestBody.query_type,
      query_preview: actualQuery.toString().slice(0, 200) + (actualQuery.toString().length > 200 ? '...' : ''),
      query_length: actualQuery.toString().length,
      search_type: requestBody.search_type || 'unknown',
      limit: requestBody.limit || 0,
      query_id: query.id,
      timestamp: new Date().toISOString()
    });

    // Log what we're actually executing
    logProcessor(`Executing vector query: ${actualQuery}`);

    // Execute vector search
    const response = await fetch(apiConfig.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Vector DB API Error (${response.status}): ${errorData}`);
    }

    const result = await response.json();
    return result;

  } catch (error) {
    logError('Processor', `Vector query execution failed for ${query.id}`, error);
    throw error;
  }
}

/**
 * Execute a GraphQL query using instruction kits for schema validation
 */
async function executeGraphQLQuery(
  query: ExecutableQuery,
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<any> {
  try {
    // Build the actual query (server-side only)
    const { actualQuery, requestBody } = await buildQueryFromSpecification(query, apiConfig);

    // PII-Safe database request logging
    logProcessor('GraphQL API Request (PII-Safe)', {
      endpoint: apiConfig.endpoint,
      query_type: requestBody.query_type,
      query_preview: actualQuery.toString().slice(0, 200) + (actualQuery.toString().length > 200 ? '...' : ''),
      query_length: actualQuery.toString().length,
      variables_count: requestBody.variables ? Object.keys(requestBody.variables).length : 0,
      has_variables: !!requestBody.variables,
      query_id: query.id,
      timestamp: new Date().toISOString()
    });

    // Log what we're actually executing
    logProcessor(`Executing GraphQL query: ${actualQuery}`);

    // FIXED: Use the correct Neo4j GraphQL endpoint directly (not Lambda)
    const neo4jGraphqlUrl = process.env.NEO4J_GRAPHQL_URL || 'https://9ed6056e-graphql.production-orch-0359.neo4j.io/graphql';
    const neo4jApiKey = process.env.NEO4J_GRAPHQL_API_KEY;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // Add authentication if API key is available
    if (neo4jApiKey) {
      headers['Authorization'] = `Bearer ${neo4jApiKey}`;
    }

    logProcessor('Executing GraphQL query directly to Neo4j API', {
      endpoint: neo4jGraphqlUrl,
      has_auth: !!neo4jApiKey,
      query_preview: requestBody.query.slice(0, 100) + '...',
      variables_count: requestBody.variables ? Object.keys(requestBody.variables).length : 0
    });

    // Execute GraphQL query directly to Neo4j GraphQL API (NOT Lambda)
    const response = await fetch(neo4jGraphqlUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        query: requestBody.query,  // Direct GraphQL, no query_type wrapper
        variables: requestBody.variables
      })
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`GraphQL API Error (${response.status}): ${errorData}`);
    }

    const result = await response.json();
    return result;

  } catch (error) {
    logError('Processor', `GraphQL query execution failed for ${query.id}`, error);
    throw error;
  }
}

/**
 * Build actual queries from specifications using instruction kits (server-side only)
 */
/**
 * Build actual queries from specifications using instruction kits (server-side only)
 * SIMPLIFIED VERSION - Let Lambda handle all the complex query building
 */
async function buildQueryFromSpecification(
  query: ExecutableQuery,
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<{ actualQuery: any; requestBody: any }> {
  // Log function input
  logProcessor('buildQueryFromSpecification - Input', {
    query_id: query.id,
    database: query.database,
    query_preview: query.query.slice(0, 100) + (query.query.length > 100 ? '...' : ''),
    query_length: query.query.length,
    has_params: !!query.params,
    params_count: query.params?.length || 0,
    is_specification: query.query.startsWith('SPECIFICATION:'),
    timestamp: new Date().toISOString()
  });

  // Check if this is a specification-based query
  if (query.query.startsWith('SPECIFICATION:')) {
    // Send the SPECIFICATION directly to Lambda - let it handle everything!
    logProcessor('Using SPECIFICATION mode - delegating to Lambda', {
      query_id: query.id,
      database: query.database,
      specification_preview: query.query.slice(0, 150) + '...',
      timestamp: new Date().toISOString()
    });

    const result = {
      actualQuery: query.query, // This will show in logs
      requestBody: {
        query_type: query.database,
        query: query.query // Lambda will parse "SPECIFICATION:{...}" and build the actual query
      }
    };

    logProcessor('buildQueryFromSpecification - Output (SPECIFICATION)', {
      query_id: query.id,
      actual_query_preview: result.actualQuery.slice(0, 100) + '...',
      request_body_type: result.requestBody.query_type,
      timestamp: new Date().toISOString()
    });

    return result;
  }

  // For legacy queries, return as-is
  const result = {
    actualQuery: query.query,
    requestBody: {
      query_type: query.database,
      query: query.query,
      params: query.params
    }
  };

  logProcessor('buildQueryFromSpecification - Output (Legacy)', {
    query_id: query.id,
    actual_query_preview: result.actualQuery.slice(0, 100) + (result.actualQuery.length > 100 ? '...' : ''),
    request_body_type: result.requestBody.query_type,
    has_params: !!result.requestBody.params,
    timestamp: new Date().toISOString()
  });

  return result;
}

/**
 * Build PostgreSQL query from specification using instruction kit
 */
async function buildPostgresFromSpec(specification: any, apiConfig: any) {
  try {
    // Get schema ONLY when needed, server-side only
    const schemaKit = await getInstructionKit('postgres_table_names');

    logProcessor('Retrieved PostgreSQL schema kit', {
      schema_kit_type: typeof schemaKit,
      schema_kit_length: JSON.stringify(schemaKit).length,
      timestamp: new Date().toISOString()
    });

    // Parse the content if it's a string (which it is!)
    let parsedSchema;
    if (typeof schemaKit === 'string') {
      parsedSchema = JSON.parse(schemaKit);
    } else if (typeof schemaKit === 'object' && schemaKit.content) {
      // If it's already an object with content property
      if (typeof schemaKit.content === 'string') {
        parsedSchema = JSON.parse(schemaKit.content);
      } else {
        parsedSchema = schemaKit.content;
      }
    } else {
      // If it's already a parsed object
      parsedSchema = schemaKit;
    }

    // Now safely access the tables
    const tables = Object.keys(parsedSchema.tables || {});
    const materializedViews = parsedSchema.materialized_views || [];

    logProcessor('Parsed PostgreSQL schema', {
      tables_count: tables.length,
      materialized_views_count: materializedViews.length,
      available_tables: tables.slice(0, 10), // First 10 tables only
      available_views: materializedViews.slice(0, 5), // First 5 views only
      timestamp: new Date().toISOString()
    });

    let actualQuery: string;
    let params: any[];

    switch (specification.intent) {
      case 'find_cards':
        if (specification.criteria.filters?.className) {
          actualQuery = `SELECT c.card_id, c.name, c.mana_cost, c.text FROM cards c
                         JOIN classes cl ON c.class_id = cl.class_id
                         WHERE cl.name = $1 LIMIT $2`;
          params = [specification.criteria.filters.className, specification.criteria.limit || 10];
        } else {
          actualQuery = `SELECT card_id, name, mana_cost, text FROM cards
                         WHERE LOWER(name) LIKE LOWER($1) LIMIT $2`;
          params = [`%${specification.criteria.searchTerms?.[0] || ''}%`, specification.criteria.limit || 10];
        }
        break;

      case 'search_general':
        const searchTerm = specification.criteria.searchTerms?.[0] || '';

        // Check if you have card_embeddings table and embedding capability
        const hasEmbeddingSupport = process.env.OPENAI_API_KEY;
        const hasEmbeddingTable = tables.includes('card_embeddings');

        if (hasEmbeddingSupport && hasEmbeddingTable) {
          try {
            // Use OpenAI embeddings
            const embedding = await convertTextToEmbedding(searchTerm);

            // Convert embedding array to PostgreSQL vector format
            const embeddingVector = `[${embedding.join(',')}]`;

            actualQuery = `
              SELECT c.card_id, c.name, c.text, c.mana_cost,
                    (ce.embedding <=> $1::vector) AS similarity_distance
              FROM cards c
              JOIN card_embeddings ce ON c.card_id = ce.card_id
              ORDER BY ce.embedding <=> $1::vector
              LIMIT $2
            `;
            params = [embeddingVector, specification.criteria.limit || 10];

          } catch (embeddingError) {
            logError('Processor', 'Embedding failed, falling back to text search', embeddingError);
            // Fallback to simple text search
            const fallback = buildSimpleTextSearch(searchTerm, specification.criteria.limit || 10);
            actualQuery = fallback.actualQuery;
            params = fallback.requestBody.params;
          }
        } else {
          // Use PostgreSQL full-text search as fallback
          const fallback = buildSimpleTextSearch(searchTerm, specification.criteria.limit || 10);
          actualQuery = fallback.actualQuery;
          params = fallback.requestBody.params;
        }
        break;

      case 'find_decks':
        // Use materialized view if available
        if (materializedViews.includes('top_decks_by_class')) {
          actualQuery = `SELECT * FROM top_decks_by_class
                         WHERE class_name = $1 AND format_name = $2
                         ORDER BY overall_winrate DESC LIMIT $3`;
          params = [
            specification.criteria.filters?.className || 'Mage',
            specification.criteria.filters?.format || 'Standard',
            specification.criteria.limit || 5
          ];
        } else {
          // Fallback to regular tables
          actualQuery = `SELECT d.deck_id, d.name, cl.name as class_name
                         FROM decks d JOIN classes cl ON d.class_id = cl.class_id
                         WHERE cl.name = $1 LIMIT $2`;
          params = [specification.criteria.filters?.className || 'Mage', specification.criteria.limit || 5];
        }
        break;

      default:
        // Safe fallback
        actualQuery = `SELECT card_id, name FROM cards LIMIT $1`;
        params = [specification.criteria.limit || 10];
    }

    return {
      actualQuery,
      requestBody: {
        query_type: 'postgres',
        query: actualQuery,
        params
      }
    };
  } catch (error) {
    logError('Processor', 'Error building Postgres query from specification', error);
    throw error;
  }
}

/**
 * Build vector query from specification using instruction kit
 */
async function buildVectorFromSpec(specification: any, apiConfig: any) {
  // Get vector methods only when needed
  const vectorKit = await getInstructionKit('vector_search_methods');

  return {
    actualQuery: 'vector_search',
    requestBody: {
      query_type: 'vector',
      text: specification.criteria.searchTerms?.join(' ') || '',
      search_type: specification.intent === 'find_cards' ? 'cards' : 'archetypes',
      limit: specification.criteria.limit || 10,
      filters: specification.criteria.filters || {}
    }
  };
}

/**
 * Build GraphQL query from specification using instruction kit
 */
async function buildGraphQLFromSpec(specification: any, apiConfig: any) {
  // Get GraphQL schema only when needed
  const graphqlKit = await getInstructionKit('graphql_schema');

  let query: string;
  let variables: any;

  switch (specification.intent) {
    case 'find_synergies':
      query = `query CardSynergies($cardName: String!, $limit: Int!) {
        cards(where: { name: $cardName }) {
          name
          synergizesWithCards(options: { limit: $limit }) {
            name
            mana_cost
          }
        }
      }`;
      variables = {
        cardName: specification.criteria.searchTerms?.[0] || '',
        limit: specification.criteria.limit || 5
      };
      break;

    default:
      query = `query AllCards($limit: Int!) {
        cards(options: { limit: $limit }) { name mana_cost }
      }`;
      variables = { limit: specification.criteria.limit || 10 };
  }

  return {
    actualQuery: query,
    requestBody: {
      query_type: 'graphql',
      query,
      variables
    }
  };
}

/**
 * Determine vector search intent from query parameters (legacy support)
 */
function determineVectorSearchIntent(query: ExecutableQuery): any {
  // Extract search parameters from the query
  let searchTerm = '';
  let searchType = 'cards';
  let filters = {};
  let limit = 10;

  // Parse parameters
  if (Array.isArray(query.params) && query.params.length > 0) {
    if (typeof query.params[0] === 'string') {
      searchTerm = query.params[0];
    } else if (typeof query.params[0] === 'object' && query.params[0] !== null) {
      const paramObj = query.params[0];
      searchTerm = paramObj.text || paramObj.search_term || '';
      searchType = paramObj.search_type || 'cards';
      filters = paramObj.filters || {};
      limit = paramObj.limit || 10;
    }
  }

  // Determine entity type from query context
  if (query.id.includes('deck') || query.purpose.toLowerCase().includes('deck')) {
    searchType = 'decks';
  } else if (query.id.includes('archetype') || query.purpose.toLowerCase().includes('archetype')) {
    searchType = 'archetypes';
  } else if (query.id.includes('card') || query.purpose.toLowerCase().includes('card')) {
    searchType = 'cards';
  }

  return {
    text: searchTerm,
    search_type: searchType,
    filters,
    limit
  };
}

/**
 * Execute multiple queries in parallel
 *
 * @param queries The queries to execute
 * @param apiConfig API configuration
 * @returns The query results
 */
export async function executeQueries(
  queries: ExecutableQuery[],
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<{ [queryId: string]: QueryResult }> {
  const startTime = Date.now();

  // Log detailed information about the batch of queries
  logProcessor(`Executing ${queries.length} queries`, {
    queryIds: queries.map(q => q.id),
    queryTypes: queries.map(q => q.database),
    queryPriorities: queries.map(q => q.priority),
    queryPurposes: queries.map(q => q.purpose)
  });

  // Log batch execution start
  logDatabaseOperation('batch', `Starting batch execution of ${queries.length} queries`, {
    queryIds: queries.map(q => q.id),
    databases: queries.map(q => q.database),
    priorities: queries.map(q => q.priority)
  });

  // Execute all queries in parallel
  const queryPromises = queries.map(query => executeQuery(query, apiConfig));
  const results = await Promise.all(queryPromises);

  // Create a map of query ID to result
  const resultMap: { [queryId: string]: QueryResult } = {};
  queries.forEach((query, index) => {
    resultMap[query.id] = results[index];
  });

  const successCount = results.filter(r => r.success).length;
  const failureCount = results.filter(r => !r.success).length;
  const executionTime = Date.now() - startTime;

  // Log detailed information about the batch results
  logProcessor(`Completed execution of ${queries.length} queries`, {
    executionTime,
    successCount,
    failureCount,
    successRate: `${Math.round((successCount / queries.length) * 100)}%`,
    queryIds: queries.map(query => query.id),
    successfulQueries: queries.filter((_, i) => results[i].success).map(query => query.id),
    failedQueries: queries.filter((_, i) => !results[i].success).map(query => query.id)
  });

  // Log batch execution completion
  logDatabaseOperation('batch', `Completed batch execution of ${queries.length} queries`, {
    executionTime,
    successCount,
    failureCount,
    successRate: `${Math.round((successCount / queries.length) * 100)}%`
  });

  // Log all query results summary (structured, no full data dump)
  logProcessor('All Query Results Summary (Structured)', {
    total_queries: Object.keys(resultMap).length,
    successful_queries: Object.values(resultMap).filter(r => r.success).length,
    failed_queries: Object.values(resultMap).filter(r => !r.success).length,
    hasData: Object.values(resultMap).some(r => r.success && r.rowCount !== undefined && r.rowCount > 0),
    total_rows: Object.values(resultMap).reduce((sum, r) => sum + (r.rowCount || 0), 0),
    query_ids: Object.keys(resultMap),
    timestamp: new Date().toISOString()
  });

  return resultMap;
}

/**
 * Execute conditional queries based on conditions and previous results
 *
 * @param conditionalQueries The conditional queries to potentially execute
 * @param previousResults Results from previous queries
 * @param apiConfig API configuration
 * @returns The query results
 */
export async function executeConditionalQueries(
  conditionalQueries: ConditionalQuery[],
  previousResults: { [queryId: string]: QueryResult },
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<{ [queryId: string]: QueryResult }> {
  // Log the start of conditional query evaluation
  logProcessor(`Evaluating ${conditionalQueries.length} conditional queries`, {
    conditionalQueryIds: conditionalQueries.map(q => q.id),
    conditions: conditionalQueries.map(q => q.condition)
  });

  // Filter queries based on conditions
  const queriesToExecute: ExecutableQuery[] = [];
  const skippedQueries: string[] = [];
  const conditionResults: { [queryId: string]: boolean } = {};

  for (const conditionalQuery of conditionalQueries) {
    // Evaluate the condition and log the result
    const shouldExecute = evaluateCondition(conditionalQuery.condition, previousResults);
    conditionResults[conditionalQuery.id] = shouldExecute;

    // Log the condition evaluation
    logProcessor(`Evaluated condition for query ${conditionalQuery.id}`, {
      queryId: conditionalQuery.id,
      condition: conditionalQuery.condition,
      shouldExecute,
      previousResultsUsed: Object.keys(previousResults)
    });

    if (shouldExecute) {
      // Convert to executable query
      queriesToExecute.push({
        id: conditionalQuery.id,
        database: conditionalQuery.database,
        query: conditionalQuery.query,
        params: conditionalQuery.params,
        purpose: conditionalQuery.purpose,
        priority: 'important' // Default priority for conditional queries
      });
    } else {
      skippedQueries.push(conditionalQuery.id);
    }
  }

  // Log detailed information about the conditional queries
  logProcessor(`Executing ${queriesToExecute.length} conditional queries out of ${conditionalQueries.length} total`, {
    queriesToExecute: queriesToExecute.map(q => q.id),
    skippedQueries,
    conditionResults
  });

  // Log the conditional execution decision
  logDatabaseOperation('conditional', `Conditional query evaluation complete`, {
    totalQueries: conditionalQueries.length,
    queriesToExecute: queriesToExecute.length,
    skippedQueries: skippedQueries.length,
    executingQueryIds: queriesToExecute.map(q => q.id),
    skippedQueryIds: skippedQueries
  });

  // Execute the filtered queries
  if (queriesToExecute.length === 0) {
    logProcessor('No conditional queries to execute');
    return {};
  }

  return executeQueries(queriesToExecute, apiConfig);
}

/**
 * Evaluate a condition based on previous query results
 *
 * @param condition The condition to evaluate
 * @param previousResults Results from previous queries
 * @returns Whether the condition is met
 */
function evaluateCondition(
  condition: string,
  previousResults: { [queryId: string]: QueryResult }
): boolean {
  // Handle common condition patterns
  switch (condition) {
    case 'if_card_found':
      return hasSuccessfulQueryWithRows(previousResults, ['card_search', 'vector_search']);

    case 'if_specific_class_mentioned':
      return hasSuccessfulQueryWithRows(previousResults, ['class_performance']);

    case 'if_specific_matchup_mentioned':
      return hasSuccessfulQueryWithRows(previousResults, ['strategy_concepts']);

    case 'if_budget_specified':
    case 'if_playstyle_specified':
      return hasSuccessfulQueryWithRows(previousResults, ['recommended_decks']);

    case 'if_specific_topic_detected':
      return hasSuccessfulQueryWithRows(previousResults, ['vector_general_search']);

    case 'if_card_details_successful':
      return hasSuccessfulQueryWithRows(previousResults, ['deck_card_details']);

    case 'if_synergies_found':
      return hasSuccessfulQueryWithRows(previousResults, ['deck_synergies']);

    default:
      // For unknown conditions, default to false
      logProcessor(`Unknown condition: ${condition}`, { defaultValue: false });
      return false;
  }
}

/**
 * Check if any of the specified queries were successful and returned rows
 *
 * @param results Query results
 * @param queryIds Query IDs to check
 * @returns Whether any of the queries were successful and returned rows
 */
function hasSuccessfulQueryWithRows(
  results: { [queryId: string]: QueryResult },
  queryIds: string[]
): boolean {
  for (const queryId of queryIds) {
    const result = results[queryId];
    if (result && result.success && result.rowCount && result.rowCount > 0) {
      return true;
    }
  }
  return false;
}

/**
 * Analyze data sufficiency based on query results
 *
 * @param queryResults Query results
 * @param intent User intent
 * @param requiredElements Required elements for the response
 * @returns Data sufficiency analysis
 */
export function analyzeDataSufficiency(
  queryResults: { [queryId: string]: QueryResult },
  intent: string,
  requiredElements: string[]
): DataSufficiencyCheck {
  // Log the start of data sufficiency analysis
  logProcessor('Starting data sufficiency analysis', {
    intent,
    requiredElements,
    availableQueryResults: Object.keys(queryResults)
  });

  // Check if critical queries were successful
  const criticalQueryIds = getCriticalQueryIds(intent);

  // Log critical query information
  logProcessor('Identified critical queries for intent', {
    intent,
    criticalQueryIds
  });

  // Check each critical query
  const criticalQueryStatus: { [queryId: string]: boolean } = {};
  criticalQueryIds.forEach(queryId => {
    const isSuccessful = queryResults[queryId] && queryResults[queryId].success;
    criticalQueryStatus[queryId] = isSuccessful;
  });

  const hasCriticalData = criticalQueryIds.every(
    queryId => queryResults[queryId] && queryResults[queryId].success
  );

  // Log critical data status
  logProcessor('Critical data status', {
    hasCriticalData,
    criticalQueryStatus
  });

  // Determine missing elements
  const missingElements = determineMissingElements(queryResults, requiredElements);

  // Log missing elements
  logProcessor('Missing elements analysis', {
    requiredElements,
    missingElements,
    missingCount: missingElements.length
  });

  // Calculate confidence level
  const confidenceLevel = calculateConfidenceLevel(queryResults, intent, missingElements);

  // Determine recommended action
  const recommendedAction = determineRecommendedAction(hasCriticalData, missingElements);

  // Log recommended action
  logProcessor('Determined recommended action', {
    recommendedAction,
    confidenceLevel,
    hasCriticalData,
    missingElementsCount: missingElements.length
  });

  const analysis: DataSufficiencyCheck = {
    hasCriticalData,
    hasEnoughDetail: missingElements.length === 0,
    needsAdditionalQueries: missingElements.length > 0,
    missingElements,
    confidenceLevel,
    recommendedAction
  };

  // Log the complete analysis
  logProcessor('Data sufficiency analysis complete', analysis);

  // Log the data sufficiency decision
  logDatabaseOperation('analysis', 'Data sufficiency analysis', {
    intent,
    hasCriticalData,
    missingElements,
    confidenceLevel,
    recommendedAction
  });

  return analysis;
}

/**
 * Get critical query IDs for a specific intent
 *
 * @param intent User intent
 * @returns Critical query IDs
 */
function getCriticalQueryIds(intent: string): string[] {
  switch (intent.toLowerCase()) {
    case 'deck_analysis':
      return ['deck_card_details'];

    case 'card_inquiry':
      return ['card_search'];

    case 'meta_analysis':
      return ['meta_decks'];

    case 'strategy_question':
      return ['strategy_concepts', 'vector_strategy_search'];

    case 'deck_request':
      return ['recommended_decks'];

    default:
      return ['vector_general_search'];
  }
}

/**
 * Determine missing elements based on query results
 *
 * @param queryResults Query results
 * @param requiredElements Required elements for the response
 * @returns Missing elements
 */
function determineMissingElements(
  queryResults: { [queryId: string]: QueryResult },
  requiredElements: string[]
): string[] {
  const missingElements: string[] = [];

  // Check each required element
  for (const element of requiredElements) {
    switch (element) {
      case 'deck_archetype':
        if (!hasSuccessfulQueryWithRows(queryResults, ['similar_decks', 'archetype_analysis'])) {
          missingElements.push(element);
        }
        break;

      case 'mana_curve':
      case 'synergies':
        if (!hasSuccessfulQueryWithRows(queryResults, ['deck_card_details', 'deck_synergies'])) {
          missingElements.push(element);
        }
        break;

      case 'meta_comparison':
        if (!hasSuccessfulQueryWithRows(queryResults, ['meta_decks', 'similar_decks'])) {
          missingElements.push(element);
        }
        break;

      default:
        // For unknown elements, assume they're missing if no successful queries
        if (Object.values(queryResults).every(result => !result.success)) {
          missingElements.push(element);
        }
        break;
    }
  }

  return missingElements;
}

/**
 * Calculate confidence level based on query results
 *
 * @param queryResults Query results
 * @param intent User intent
 * @param missingElements Missing elements
 * @returns Confidence level (0-1)
 */
function calculateConfidenceLevel(
  queryResults: { [queryId: string]: QueryResult },
  intent: string,
  missingElements: string[]
): number {
  // Start with base confidence
  let confidence = 0.5;

  // Adjust based on critical data
  const criticalQueryIds = getCriticalQueryIds(intent);
  const criticalSuccessRate = criticalQueryIds.filter(
    queryId => queryResults[queryId] && queryResults[queryId].success
  ).length / criticalQueryIds.length;

  confidence = criticalSuccessRate * 0.8; // Max 0.8 from critical queries

  // Adjust based on missing elements
  if (missingElements.length === 0) {
    confidence += 0.2; // Full confidence if no missing elements
  } else {
    // Reduce confidence based on proportion of missing elements
    confidence += 0.2 * (1 - missingElements.length / 5);
  }

  return Math.max(0.1, Math.min(1.0, confidence));
}

/**
 * Determine recommended action based on data sufficiency
 *
 * @param hasCriticalData Whether critical data is available
 * @param missingElements Missing elements
 * @returns Recommended action
 */
function determineRecommendedAction(
  hasCriticalData: boolean,
  missingElements: string[]
): 'synthesize' | 'query_more' | 'partial_response' {
  if (!hasCriticalData) {
    return 'partial_response';
  }

  if (missingElements.length > 0) {
    return 'query_more';
  }

  return 'synthesize';
}