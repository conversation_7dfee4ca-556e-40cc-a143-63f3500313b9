/**
 * Claude Processor
 *
 * This module handles the processing layer of the Two-Claude Architecture.
 * It's responsible for:
 * 1. Executing queries and gathering data through processorDb.ts
 * 2. Analyzing data sufficiency and making additional queries as needed
 * 3. Synthesizing data into natural, insightful responses
 * 4. Applying consistent formatting and style
 * 5. Ensuring deck codes are properly formatted
 * 6. Hiding technical implementation details from the user
 */

import Anthropic from '@anthropic-ai/sdk';
import { logProcessor, logError } from '@/lib/utils/claudeLogger';
import {
  executeQueries,
  executeConditionalQueries
} from '@/lib/claude/processorDb';
import {
  ExecutableQuery,
  OrchestratorPackage,
  QueryResult
} from '@/lib/types/queryTypes';

// TypeScript interfaces for query planner response
interface QueryPlan {
  id: string;
  database: 'postgres' | 'vector' | 'neo4j';
  type: string;
  criteria?: Record<string, any>;
  fields?: string[];
  nodes?: string[];
  depth?: number;
}

interface ResponseExpectation {
  assemble_summary: boolean;
  include?: string[];
  style?: string;
}

interface QueryPlannerResponse {
  queries: QueryPlan[];
  response_expectation: ResponseExpectation;
}

// Legacy interfaces for backward compatibility
interface DeckInfo {
  name: string;
  class?: string;
  winrate?: string;
  notes?: string;
}

interface CardInfo {
  name: string;
  mana_cost?: number;
  type?: string;
  text?: string;
  class?: string;
}

interface SynergyInfo {
  card1: string;
  card2: string;
  synergy_strength?: number;
  description?: string;
}

// Expanded structured processor data to support polymorphic results
interface StructuredProcessorData {
  decks?: DeckInfo[];
  cards?: CardInfo[];
  synergies?: SynergyInfo[];
  summary?: string;
  confidence?: number;
  dataQuality?: 'high' | 'medium' | 'low';
  rawResults?: Record<string, any>;
  error?: string;
  warnings?: string[];
  partialFailures?: string[];
}

interface ProcessorResponse {
  structuredResponse: StructuredProcessorData;
  rawResponse: string;
  queryResults: any;
  metadata: {
    queriesExecuted: number;
    successfulQueries: number;
    hasData: boolean;
    confidence: number;
    dataQuality: string;
    warnings?: string[];
    partialFailures?: string[];
    survivableFailures?: boolean;
  };
}

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

/**
 * Processor system prompt - STRUCTURED JSON OUTPUT VERSION
 */

const PROCESSOR_SYSTEM_PROMPT = `You are a multi-database query planner for a Hearthstone AI assistant.

You will be given a user message and a description of the available databases. Your job is to:
- Analyze the user's request
- Break it down into individual data retrieval intents
- Select the appropriate database(s)
- Output a list of database query specifications
- Include response formatting preferences

Available databases:
- "postgres": Structured data such as deck metadata, card metadata, winrates, deck codes, minion lists.
- "vector": Embedded text from HSReplay, Reddit, and guide content. Use for unstructured or vague queries.
- "neo4j": A knowledge graph of card relationships and deck synergies. Use when the user asks how cards interact or for synergy-based reasoning.

Your output must be valid JSON with the following structure:

{
  "queries": [
    {
      "id": "deck_lookup",
      "database": "postgres",
      "type": "deck_detail",
      "criteria": {
        "deck_name": "Protoss Mage"
      },
      "fields": ["deck_code", "minions", "winrate", "format"]
    },
    {
      "id": "card_synergies",
      "database": "neo4j",
      "type": "synergy_lookup",
      "nodes": ["Protoss Mage"],
      "depth": 2
    }
  ],
  "response_expectation": {
    "assemble_summary": true,
    "include": ["deck_code", "minions", "strategy"],
    "style": "casual"
  }
}

If no queries are needed, return:

{
  "queries": [],
  "response_expectation": {
    "assemble_summary": false
  }
}

Never include markdown formatting, commentary, or natural language text. Only return valid JSON in the exact format above.`;
/**
 * Execute a dynamic query plan across multiple databases with enhanced error handling
 */
async function executeQueryPlan(
  queryPlan: QueryPlannerResponse,
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<{ [queryId: string]: QueryResult }> {
  const results: { [queryId: string]: QueryResult } = {};
  const warnings: string[] = [];
  const partialFailures: string[] = [];

  logProcessor('Executing dynamic query plan', {
    queryCount: queryPlan.queries.length,
    databases: queryPlan.queries.map(q => q.database),
    queryTypes: queryPlan.queries.map(q => q.type)
  });

  // Execute all queries in parallel
  const queryPromises = queryPlan.queries.map(async (query) => {
    try {
      let result: QueryResult;

      switch (query.database) {
        case 'postgres':
          result = await executePostgresQueryPlan(query, apiConfig);
          break;
        case 'vector':
          result = await executeVectorQueryPlan(query, apiConfig);
          break;
        case 'neo4j':
          result = await executeNeo4jQueryPlan(query, apiConfig);
          break;
        default:
          throw new Error(`Unsupported database: ${query.database}`);
      }

      // Check for survivable failures (queries that failed but system can continue)
      if (!result.success) {
        const isNonCritical = query.id.includes('synerg') || query.id.includes('meta_context');
        if (isNonCritical) {
          warnings.push(`Non-critical query ${query.id} failed: ${result.error}`);
          result.survivableFailure = true;
        } else {
          partialFailures.push(`Critical query ${query.id} failed: ${result.error}`);
        }
      }

      return { queryId: query.id, result };
    } catch (error: any) {
      logError('Processor', `Failed to execute query ${query.id}`, error);

      // Determine if this is a survivable failure
      const isNonCritical = query.id.includes('synerg') || query.id.includes('meta_context');
      const errorMessage = error.message || 'Unknown error';

      if (isNonCritical) {
        warnings.push(`Non-critical query ${query.id} failed: ${errorMessage}`);
      } else {
        partialFailures.push(`Critical query ${query.id} failed: ${errorMessage}`);
      }

      return {
        queryId: query.id,
        result: {
          success: false,
          data: null,
          error: errorMessage,
          executionTime: 0,
          survivableFailure: isNonCritical
        }
      };
    }
  });

  const queryResults = await Promise.all(queryPromises);

  // Build results map
  queryResults.forEach(({ queryId, result }) => {
    results[queryId] = result;
  });

  const successfulQueries = Object.values(results).filter(r => r.success).length;
  const totalQueries = queryPlan.queries.length;
  const survivableFailures = Object.values(results).filter(r => !r.success && r.survivableFailure).length;

  logProcessor('Query plan execution complete', {
    totalQueries,
    successfulQueries,
    failedQueries: totalQueries - successfulQueries,
    survivableFailures,
    warnings: warnings.length,
    partialFailures: partialFailures.length,
    canContinue: successfulQueries > 0 || survivableFailures > 0
  });

  // Store warnings and failures in results metadata for later use
  (results as any)._metadata = {
    warnings,
    partialFailures,
    survivableFailures: survivableFailures > 0,
    canContinue: successfulQueries > 0 || survivableFailures > 0
  };

  return results;
}

/**
 * Execute a PostgreSQL query from a query plan with enhanced query type support
 */
async function executePostgresQueryPlan(
  query: QueryPlan,
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<QueryResult> {
  const startTime = Date.now();

  try {
    let sqlQuery: string;
    let params: any[] = [];

    // Build SQL based on query type
    switch (query.type) {
      case 'deck_detail':
        if (query.criteria?.deck_code) {
          // Direct deck code lookup - FIXED SCHEMA
          sqlQuery = `SELECT tdc.deck_id, d.name, tdc.class_name, tdc.overall_winrate as winrate,
                             d.deck_code, tdc.format_name, tdc.overall_winrate,
                             tdc.dust_cost, tdc.archetype_name, tdc.rank
                      FROM top_decks_by_class tdc
                      JOIN decks d ON tdc.deck_id = d.deck_id
                      WHERE d.deck_code = $1
                      LIMIT 1`;
          params = [query.criteria.deck_code];
        } else if (query.criteria?.deck_name) {
          sqlQuery = `SELECT tdc.deck_id, d.name, tdc.class_name, tdc.overall_winrate as winrate,
                             d.deck_code, tdc.format_name, tdc.overall_winrate,
                             tdc.dust_cost, tdc.archetype_name, tdc.rank
                      FROM top_decks_by_class tdc
                      JOIN decks d ON tdc.deck_id = d.deck_id
                      WHERE LOWER(d.name) LIKE LOWER($1)
                      LIMIT 5`;
          params = [`%${query.criteria.deck_name}%`];
        } else if (query.criteria?.class_name) {
          sqlQuery = `SELECT tdc.deck_id, d.name, tdc.class_name, tdc.overall_winrate as winrate,
                             d.deck_code, tdc.format_name, tdc.overall_winrate,
                             tdc.dust_cost, tdc.archetype_name, tdc.rank
                      FROM top_decks_by_class tdc
                      JOIN decks d ON tdc.deck_id = d.deck_id
                      WHERE tdc.class_name = $1
                      ORDER BY tdc.overall_winrate DESC
                      LIMIT 10`;
          params = [query.criteria.class_name];
        } else {
          sqlQuery = `SELECT tdc.deck_id, d.name, tdc.class_name, tdc.overall_winrate as winrate,
                             d.deck_code, tdc.format_name, tdc.overall_winrate,
                             tdc.dust_cost, tdc.archetype_name, tdc.rank
                      FROM top_decks_by_class tdc
                      JOIN decks d ON tdc.deck_id = d.deck_id
                      ORDER BY tdc.overall_winrate DESC
                      LIMIT 10`;
        }
        break;

      case 'deck_list':
        // New query type for listing decks by criteria - FIXED SCHEMA
        {
          const whereConditions: string[] = [];
          let paramIndex = 1;

          if (query.criteria?.class_name) {
            whereConditions.push(`tdc.class_name = $${paramIndex}`);
            params.push(query.criteria.class_name);
            paramIndex++;
          }

          if (query.criteria?.format_name) {
            whereConditions.push(`tdc.format_name = $${paramIndex}`);
            params.push(query.criteria.format_name);
            paramIndex++;
          }

          if (query.criteria?.min_winrate) {
            whereConditions.push(`tdc.overall_winrate >= $${paramIndex}`);
            params.push(query.criteria.min_winrate);
            paramIndex++;
          }

          const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
          const limit = query.criteria?.limit || 10;

          sqlQuery = `SELECT tdc.deck_id, d.name, tdc.class_name, tdc.overall_winrate as winrate,
                             d.deck_code, tdc.format_name, tdc.overall_winrate,
                             tdc.dust_cost, tdc.archetype_name, tdc.rank
                      FROM top_decks_by_class tdc
                      JOIN decks d ON tdc.deck_id = d.deck_id
                      ${whereClause}
                      ORDER BY tdc.overall_winrate DESC
                      LIMIT ${limit}`;
        }
        break;

      case 'deck_performance':
        // New query type for deck performance analysis - FIXED SCHEMA
        if (query.criteria?.deck_code) {
          // Need to JOIN with decks table to get name and deck_code
          sqlQuery = `SELECT tdc.deck_id, d.name, tdc.class_name, tdc.overall_winrate as winrate,
                             d.deck_code, tdc.format_name, tdc.overall_winrate,
                             tdc.dust_cost, tdc.archetype_name, tdc.rank
                      FROM top_decks_by_class tdc
                      JOIN decks d ON tdc.deck_id = d.deck_id
                      WHERE d.deck_code = $1
                      LIMIT 1`;
          params = [query.criteria.deck_code];
        } else if (query.criteria?.class_name && query.criteria?.format_name) {
          sqlQuery = `SELECT tdc.deck_id, d.name, tdc.class_name, tdc.overall_winrate as winrate,
                             d.deck_code, tdc.format_name, tdc.overall_winrate,
                             tdc.dust_cost, tdc.archetype_name, tdc.rank
                      FROM top_decks_by_class tdc
                      JOIN decks d ON tdc.deck_id = d.deck_id
                      WHERE tdc.class_name = $1 AND tdc.format_name = $2
                      ORDER BY tdc.overall_winrate DESC
                      LIMIT ${query.criteria.limit || 5}`;
          params = [query.criteria.class_name, query.criteria.format_name];
        } else {
          throw new Error('deck_performance requires either deck_code or class_name+format_name criteria');
        }
        break;

      case 'class_decks':
        // New query type for class-based deck listing - FIXED SCHEMA
        {
          const whereConditions: string[] = [];
          let paramIndex = 1;

          if (query.criteria?.class_name) {
            whereConditions.push(`tdc.class_name = $${paramIndex}`);
            params.push(query.criteria.class_name);
            paramIndex++;
          }

          if (query.criteria?.format_name) {
            whereConditions.push(`tdc.format_name = $${paramIndex}`);
            params.push(query.criteria.format_name);
            paramIndex++;
          }

          if (query.criteria?.min_games) {
            // Assuming we have a games column or use a threshold
            whereConditions.push(`tdc.overall_winrate >= 50`); // Proxy for min_games
            paramIndex++;
          }

          const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
          const limit = query.criteria?.limit || 10;

          sqlQuery = `SELECT tdc.deck_id, d.name, tdc.class_name, tdc.overall_winrate as winrate,
                             d.deck_code, tdc.format_name, tdc.overall_winrate,
                             tdc.dust_cost, tdc.archetype_name, tdc.rank
                      FROM top_decks_by_class tdc
                      JOIN decks d ON tdc.deck_id = d.deck_id
                      ${whereClause}
                      ORDER BY tdc.overall_winrate DESC
                      LIMIT ${limit}`;
        }
        break;

      case 'card_search':
      case 'card_lookup':
        // Enhanced card search with multiple criteria - FIXED SCHEMA
        if (query.criteria?.card_name) {
          sqlQuery = `SELECT c.card_id, c.name, c.mana_cost, c.attack, c.health, c.text,
                             r.name as rarity, ct.name as type, cl.name as class
                      FROM cards c
                      LEFT JOIN rarities r ON c.rarity_id = r.rarity_id
                      LEFT JOIN card_types ct ON c.card_type_id = ct.card_type_id
                      LEFT JOIN classes cl ON c.class_id = cl.class_id
                      WHERE LOWER(c.name) LIKE LOWER($1)
                      LIMIT 10`;
          params = [`%${query.criteria.card_name}%`];
        } else if (query.criteria?.card_id) {
          sqlQuery = `SELECT c.card_id, c.name, c.mana_cost, c.attack, c.health, c.text,
                             r.name as rarity, ct.name as type, cl.name as class
                      FROM cards c
                      LEFT JOIN rarities r ON c.rarity_id = r.rarity_id
                      LEFT JOIN card_types ct ON c.card_type_id = ct.card_type_id
                      LEFT JOIN classes cl ON c.class_id = cl.class_id
                      WHERE c.card_id = $1
                      LIMIT 1`;
          params = [query.criteria.card_id];
        } else {
          sqlQuery = `SELECT c.card_id, c.name, c.mana_cost, c.attack, c.health, c.text,
                             r.name as rarity, ct.name as type, cl.name as class
                      FROM cards c
                      LEFT JOIN rarities r ON c.rarity_id = r.rarity_id
                      LEFT JOIN card_types ct ON c.card_type_id = ct.card_type_id
                      LEFT JOIN classes cl ON c.class_id = cl.class_id
                      LIMIT 10`;
        }
        break;

      default:
        throw new Error(`Unsupported PostgreSQL query type: ${query.type}. Supported types: deck_detail, deck_list, deck_performance, class_decks, card_search, card_lookup`);
    }

    // FIXED: Lambda expects simple SQL string, not parameterized queries
    // Substitute parameters into the SQL query for Lambda compatibility
    let finalQuery = sqlQuery;
    if (params && params.length > 0) {
      finalQuery = sqlQuery.replace(/\$(\d+)/g, (match, num) => {
        const paramIndex = parseInt(num) - 1;
        const param = params[paramIndex];
        // Properly escape string parameters
        if (typeof param === 'string') {
          return `'${param.replace(/'/g, "''")}'`;
        }
        return String(param);
      });
    }

    const response = await fetch(apiConfig.endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query_type: 'postgres',
        query: finalQuery  // Send final SQL string without params
      })
    });

    if (!response.ok) {
      throw new Error(`PostgreSQL API Error (${response.status}): ${await response.text()}`);
    }

    const data = await response.json();
    const executionTime = Date.now() - startTime;

    return {
      success: true,
      data,
      executionTime,
      rowCount: Array.isArray(data) ? data.length : 0
    };
  } catch (error: any) {
    return {
      success: false,
      data: null,
      error: error.message,
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Execute a Vector database query from a query plan with enhanced query type support
 */
async function executeVectorQueryPlan(
  query: QueryPlan,
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<QueryResult> {
  const startTime = Date.now();

  try {
    let requestBody: any;

    switch (query.type) {
      case 'semantic_search':
        // New query type for semantic search with comprehensive field mapping
        const searchText = (query as any).query || (query as any).content || query.criteria?.query || query.criteria?.search_text || '';
        requestBody = {
          query_type: 'vector',
          text: searchText,
          search_type: 'semantic',
          limit: (query as any).limit || query.criteria?.limit || 10
        };
        break;

      case 'cards':
      case 'archetype_search':
      default:
        // Legacy and fallback support
        requestBody = {
          query_type: 'vector',
          text: query.criteria?.search_text || query.criteria?.query || (query as any).query || (query as any).content || '',
          search_type: query.type || 'cards',
          limit: query.criteria?.limit || (query as any).limit || 10
        };
        break;
    }

    // Add timeout handling for vector search
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), apiConfig.timeout || 30000);

    let response: Response;
    try {
      response = await fetch(apiConfig.endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Vector API Error (${response.status}): ${errorText}`);
      }
    } catch (error: any) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error(`Vector search timeout after ${apiConfig.timeout || 30000}ms. This may indicate OpenAI API issues or Lambda cold start delays.`);
      }
      throw error;
    }

    const data = await response.json();
    const executionTime = Date.now() - startTime;

    return {
      success: true,
      data,
      executionTime,
      rowCount: Array.isArray(data) ? data.length : 0
    };
  } catch (error: any) {
    return {
      success: false,
      data: null,
      error: error.message,
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Execute a Neo4j query from a query plan with enhanced query type support
 */
async function executeNeo4jQueryPlan(
  query: QueryPlan,
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<QueryResult> {
  const startTime = Date.now();

  try {
    let graphqlQuery: string;
    let variables: any = {};

    switch (query.type) {
      case 'synergy_lookup':
        graphqlQuery = `query CardSynergies($cardName: String!, $limit: Int!) {
          cards(where: { name: $cardName }) {
            name
            synergizesWithCards(options: { limit: $limit }) {
              name
              mana_cost
            }
          }
        }`;
        variables = {
          cardName: query.nodes?.[0] || '',
          limit: query.depth || 5
        };
        break;

      case 'synergy_analysis':
        // New query type for multi-node synergy analysis
        if (query.nodes && query.nodes.length > 0) {
          // Build dynamic GraphQL query for multiple cards
          const cardQueries = query.nodes.map((cardName, index) => `
            card${index}: cards(where: { name: "${cardName}" }) {
              name
              synergizesWithCards(options: { limit: ${query.depth || 3} }) {
                name
                mana_cost
                type
              }
            }
          `).join('\n');

          graphqlQuery = `query MultiCardSynergies {
            ${cardQueries}
          }`;
          variables = {};
        } else {
          throw new Error('synergy_analysis requires at least one node in the nodes array');
        }
        break;

      case 'deck_improvement':
        // Map to synergy_analysis behavior for deck improvement recommendations
        // Use class_name or archetype from query criteria, or nodes array
        let targetNodes: string[] = [];

        if (query.nodes && query.nodes.length > 0) {
          targetNodes = query.nodes;
        } else if (query.criteria?.class_name) {
          // Use class name as a node for synergy analysis
          targetNodes = [query.criteria.class_name];
        } else if (query.criteria?.archetype) {
          targetNodes = [query.criteria.archetype];
        } else {
          throw new Error('deck_improvement requires nodes array, class_name, or archetype in criteria');
        }

        // Build dynamic GraphQL query for deck improvement synergies
        const improvementQueries = targetNodes.map((nodeName, index) => `
          improvement${index}: cards(where: { name: "${nodeName}" }) {
            name
            synergizesWithCards(options: { limit: ${query.depth || 2} }) {
              name
              mana_cost
              type
              class
            }
          }
        `).join('\n');

        graphqlQuery = `query DeckImprovementSynergies {
          ${improvementQueries}
        }`;
        variables = {};
        break;

      default:
        throw new Error(`Unsupported Neo4j query type: ${query.type}. Supported types: synergy_lookup, synergy_analysis, deck_improvement`);
    }

    // Use the correct Neo4j GraphQL endpoint directly
    const neo4jGraphqlUrl = process.env.NEO4J_GRAPHQL_URL || 'https://9ed6056e-graphql.production-orch-0359.neo4j.io/graphql';
    const neo4jApiKey = process.env.NEO4J_GRAPHQL_API_KEY;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // Add authentication if API key is available
    if (neo4jApiKey) {
      headers['Authorization'] = `Bearer ${neo4jApiKey}`;
    }

    const response = await fetch(neo4jGraphqlUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        query: graphqlQuery,  // Direct GraphQL, no query_type wrapper
        variables: variables
      })
    });

    if (!response.ok) {
      throw new Error(`GraphQL API Error (${response.status}): ${await response.text()}`);
    }

    const data = await response.json();
    const executionTime = Date.now() - startTime;

    return {
      success: true,
      data,
      executionTime,
      rowCount: data?.data?.cards?.length || 0
    };
  } catch (error: any) {
    return {
      success: false,
      data: null,
      error: error.message,
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Retry logic utility function for Anthropic API calls
 */
async function callAnthropicWithRetry(
  messageConfig: any,
  maxRetries: number = 5
): Promise<any> {
  let retryCount = 0;

  while (retryCount < maxRetries) {
    try {
      const response = await anthropic.messages.create(messageConfig);
      return response;
    } catch (error: any) {
      // Check if this is an overloaded error
      const isOverloaded =
        error.status === 529 ||
        (error.error && error.error.type === 'overloaded_error') ||
        (error.message && error.message.includes('Overloaded'));

      // If we've reached max retries or it's not an overloaded error, throw
      if (retryCount >= maxRetries - 1 || !isOverloaded) {
        // Enhanced error logging with input context
        logError('Processor', `Failed to call Claude after ${retryCount + 1} attempts`, {
          error: error,
          messageConfig: {
            model: messageConfig.model,
            system_prompt_length: messageConfig.system?.length || 0,
            messages_count: messageConfig.messages?.length || 0,
            max_tokens: messageConfig.max_tokens,
            temperature: messageConfig.temperature,
            stream: messageConfig.stream
          },
          retry_attempts: retryCount + 1,
          max_retries: maxRetries,
          error_type: isOverloaded ? 'overloaded' : 'other',
          timestamp: new Date().toISOString()
        });
        throw error;
      }

      // Calculate backoff time: 2^retryCount * 1000ms + random jitter
      const backoffTime = Math.pow(2, retryCount) * 1000 + Math.random() * 1000;
      retryCount++;

      logProcessor(`Claude API overloaded. Retrying in ${Math.round(backoffTime / 1000)} seconds (attempt ${retryCount} of ${maxRetries})`, {
        error: error.message || 'Overloaded',
        retryCount,
        backoffTime: `${Math.round(backoffTime / 1000)} seconds`
      });

      // Wait for the backoff time
      await new Promise(resolve => setTimeout(resolve, backoffTime));
    }
  }
}

/**
 * Summarize query results for efficient token usage
 */
/**
 * Summarize query results for efficient token usage - FIXED VERSION
 */
function summarizeQueryResults(queryResults: { [queryId: string]: QueryResult }): string {
  // Log function input
  logProcessor('summarizeQueryResults - Input', {
    query_count: Object.keys(queryResults).length,
    query_ids: Object.keys(queryResults),
    successful_queries: Object.values(queryResults).filter(r => r.success).length,
    failed_queries: Object.values(queryResults).filter(r => !r.success).length,
    timestamp: new Date().toISOString()
  });

  const summary: string[] = [];

  for (const [queryId, result] of Object.entries(queryResults)) {
    if (result.success && result.data) {
      const rowCount = result.rowCount || 0;
      summary.push(`${queryId}: ${rowCount} results (${result.executionTime}ms)`);

      // INCLUDE ACTUAL DATA, not just field names!
      if (Array.isArray(result.data) && result.data.length > 0) {
        summary.push(`  ACTUAL DATA:`);

        // For deck data, show the actual deck names
        if (queryId.includes('deck') || queryId.includes('recommended')) {
          result.data.forEach((deck: any, index: number) => {
            if (deck.name) {
              summary.push(`    ${index + 1}. ${deck.name} (${deck.class_name || 'Unknown Class'})`);
            }
          });
        } else {
          // For other data, show first few items
          result.data.slice(0, 3).forEach((item: any, index: number) => {
            summary.push(`    ${index + 1}. ${JSON.stringify(item)}`);
          });
        }
      }
    } else {
      summary.push(`${queryId}: FAILED - ${result.error || 'Unknown error'}`);
    }
  }

  const finalSummary = summary.join('\n');

  // Log function output
  logProcessor('summarizeQueryResults - Output', {
    summary_length: finalSummary.length,
    summary_lines: summary.length,
    summary_preview: finalSummary.slice(0, 200) + (finalSummary.length > 200 ? '...' : ''),
    deck_names_found: summary.filter(line => line.includes('. ') && line.includes('(')).length,
    timestamp: new Date().toISOString()
  });

  return finalSummary;
}

/**
 * New query planner function that uses Claude 2 as a dynamic query planner
 */
export async function processWithQueryPlanner(
  userMessage: string,
  chatHistory: any[],
  apiConfig: { endpoint: string; timeout: number; retryAttempts: number }
): Promise<ProcessorResponse> {
  try {
    logProcessor('Starting query planner workflow', {
      userMessage: userMessage.substring(0, 100),
      chatHistoryLength: chatHistory.length
    });

    // Format chat history for context
    const formattedMessages = (chatHistory || []).map((msg: any) => ({
      role: msg.isUser ? 'user' as const : 'assistant' as const,
      content: msg.content || msg.text
    }));

    // Add the current user message
    formattedMessages.push({
      role: 'user' as const,
      content: userMessage
    });

    // Create schema context for Claude 2 - UPDATED WITH ACTUAL SCHEMA
    const schemaContext = `
Available Database Schema:

PostgreSQL:
- decks: [deck_id, name, archetype_id, class_id, format_id, dust_cost, deck_code, url]
- cards: [card_id, name, mana_cost, attack, health, text, rarity_id, card_type_id, class_id]
- top_decks_by_class: [deck_id, class_name, archetype_name, format_name, overall_winrate, dust_cost, rank]
  Note: To get deck names and deck_codes, JOIN with decks table on deck_id

Vector Database:
- card_embeddings: [text content for semantic search]
- archetype_embeddings: [text content for deck archetype search]

Neo4j Graph:
- Card nodes: [name, mana_cost] with synergizesWith relationships
- DeckArchetype nodes: [name, key_cards, win_rate, popularity]

User Message: "${userMessage}"

Analyze this request and create a query plan to retrieve the necessary data.`;

    // Call Claude 2 as query planner
    const response = await callAnthropicWithRetry({
      model: 'claude-3-5-sonnet-20241022',
      system: PROCESSOR_SYSTEM_PROMPT,
      messages: [
        ...formattedMessages.slice(-3), // Limited context
        {
          role: 'user' as const,
          content: schemaContext
        }
      ],
      max_tokens: 4096,
      temperature: 0.3, // Lower temperature for more structured planning
      stream: false,
    });

    // Extract and parse the query plan
    const rawPlan = response.content[0] && 'text' in response.content[0]
      ? response.content[0].text
      : '';

    logProcessor('Received query plan from Claude 2', { rawPlan });

    let queryPlan: QueryPlannerResponse;
    try {
      queryPlan = JSON.parse(rawPlan);
    } catch (parseError) {
      logError('Processor', 'Failed to parse query plan JSON', { rawPlan, parseError });
      // Fallback to empty plan
      queryPlan = {
        queries: [],
        response_expectation: {
          assemble_summary: false
        }
      };
    }

    // Execute the query plan
    const queryResults = await executeQueryPlan(queryPlan, apiConfig);

    // Transform results into structured data
    const structuredData = transformQueryResultsToStructuredData(queryResults, queryPlan);

    // Extract metadata for enhanced error reporting
    const metadata = (queryResults as any)._metadata;
    const successfulQueries = Object.values(queryResults).filter(r => r.success).length;
    const totalQueries = Object.keys(queryResults).length;

    // Log detailed structured data for debugging
    logProcessor('Structured Data Generated', {
      decks: {
        count: structuredData.decks?.length || 0,
        preview: structuredData.decks?.slice(0, 2) || []
      },
      cards: {
        count: structuredData.cards?.length || 0,
        preview: structuredData.cards?.slice(0, 2) || []
      },
      synergies: {
        count: structuredData.synergies?.length || 0,
        preview: structuredData.synergies?.slice(0, 2) || []
      },
      confidence: structuredData.confidence,
      dataQuality: structuredData.dataQuality,
      summary: structuredData.summary,
      warnings: structuredData.warnings?.length || 0,
      partialFailures: structuredData.partialFailures?.length || 0
    });

    // Log individual query results for debugging
    Object.entries(queryResults).forEach(([queryId, result]) => {
      logProcessor(`Query Result Detail: ${queryId}`, {
        success: result.success,
        error: result.error || null,
        executionTime: result.executionTime,
        rowCount: result.rowCount,
        dataType: Array.isArray(result.data) ? 'array' : typeof result.data,
        dataPreview: result.success && result.data ?
          (Array.isArray(result.data) ? result.data.slice(0, 1) : result.data) : null
      });
    });

    logProcessor('Query planner workflow complete', {
      queriesExecuted: totalQueries,
      successfulQueries,
      failedQueries: totalQueries - successfulQueries,
      structuredDataKeys: Object.keys(structuredData),
      warnings: metadata?.warnings?.length || 0,
      partialFailures: metadata?.partialFailures?.length || 0,
      canContinue: metadata?.canContinue || false,
      hasUsableData: (structuredData.decks?.length || 0) + (structuredData.cards?.length || 0) + (structuredData.synergies?.length || 0) > 0
    });

    return {
      structuredResponse: structuredData,
      rawResponse: rawPlan,
      queryResults: queryResults,
      metadata: {
        queriesExecuted: totalQueries,
        successfulQueries,
        hasData: Object.values(queryResults).some(r => r.success && r.rowCount !== undefined && r.rowCount > 0),
        confidence: structuredData.confidence || 0.7,
        dataQuality: structuredData.dataQuality || "medium",
        warnings: metadata?.warnings || [],
        partialFailures: metadata?.partialFailures || [],
        survivableFailures: metadata?.survivableFailures || false
      }
    };

  } catch (error: any) {
    logError('Processor', 'Error in query planner workflow', error);
    throw error;
  }
}

/**
 * Transform query results into structured data format with improved query type mapping
 */
function transformQueryResultsToStructuredData(
  queryResults: { [queryId: string]: QueryResult },
  queryPlan: QueryPlannerResponse
): StructuredProcessorData {
  const structuredData: StructuredProcessorData = {
    rawResults: queryResults
  };

  // Create a mapping of query IDs to their types for better classification
  const queryTypeMap = new Map<string, string>();
  queryPlan.queries.forEach(query => {
    queryTypeMap.set(query.id, query.type);
  });

  // Extract warnings and failures from metadata if available
  const metadata = (queryResults as any)._metadata;
  if (metadata) {
    structuredData.warnings = metadata.warnings;
    structuredData.partialFailures = metadata.partialFailures;
  }

  // Extract decks from results using query type mapping with enhanced logging
  const deckResults = Object.entries(queryResults).filter(([id, result]) => {
    const queryType = queryTypeMap.get(id);
    const isDeckQuery = result.success && result.data &&
           (queryType === 'deck_detail' || queryType === 'deck_list' || queryType === 'class_decks' || queryType === 'deck_performance' ||
            id.includes('deck') || id.includes('current_deck'));

    // Log filtering decision for debugging
    logProcessor(`Deck extraction filter for ${id}`, {
      queryType,
      success: result.success,
      hasData: !!result.data,
      isDeckQuery,
      dataType: Array.isArray(result.data) ? 'array' : typeof result.data,
      dataLength: Array.isArray(result.data) ? result.data.length : 'N/A'
    });

    return isDeckQuery;
  });

  if (deckResults.length > 0) {
    structuredData.decks = [];
    deckResults.forEach(([queryId, result]) => {
      // Handle different data structures that might be returned
      let dataArray: any[] = [];

      if (Array.isArray(result.data)) {
        dataArray = result.data;
      } else if (result.data && typeof result.data === 'object') {
        // Handle nested data structures
        if (result.data.data && Array.isArray(result.data.data)) {
          dataArray = result.data.data;
        } else if (result.data.rows && Array.isArray(result.data.rows)) {
          dataArray = result.data.rows;
        } else {
          // Single object, wrap in array
          dataArray = [result.data];
        }
      }

      logProcessor(`Processing deck data for ${queryId}`, {
        originalDataType: typeof result.data,
        extractedArrayLength: dataArray.length,
        sampleData: dataArray.length > 0 ? dataArray[0] : null
      });

      dataArray.forEach((deck: any) => {
        if (deck && (deck.name || deck.deck_id)) {
          structuredData.decks!.push({
            name: deck.name || deck.deck_name || `Deck ${deck.deck_id}` || 'Unknown Deck',
            class: deck.class_name || deck.class || 'Unknown Class',
            winrate: deck.winrate || deck.overall_winrate || 'Unknown',
            notes: `Format: ${deck.format_name || 'Unknown'}`
          });
        }
      });
    });
  }

  // Extract cards from results using query type mapping
  const cardResults = Object.entries(queryResults).filter(([id, result]) => {
    const queryType = queryTypeMap.get(id);
    return result.success && result.data &&
           (queryType === 'card_search' || queryType === 'card_lookup' ||
            id.includes('card'));
  });

  if (cardResults.length > 0) {
    structuredData.cards = [];
    cardResults.forEach(([_queryId, result]) => {
      if (Array.isArray(result.data)) {
        result.data.forEach((card: any) => {
          structuredData.cards!.push({
            name: card.name || 'Unknown Card',
            mana_cost: card.mana_cost,
            type: card.type,
            text: card.text,
            class: card.class
          });
        });
      }
    });
  }

  // Extract synergies from results using query type mapping with enhanced support
  const synergyResults = Object.entries(queryResults).filter(([id, result]) => {
    const queryType = queryTypeMap.get(id);
    const isSynergyQuery = result.success && result.data &&
           (queryType === 'synergy_lookup' || queryType === 'synergy_analysis' || queryType === 'deck_improvement' ||
            id.includes('synerg') || id.includes('improvement'));

    // Log filtering decision for debugging
    logProcessor(`Synergy extraction filter for ${id}`, {
      queryType,
      success: result.success,
      hasData: !!result.data,
      isSynergyQuery,
      dataType: typeof result.data
    });

    return isSynergyQuery;
  });

  if (synergyResults.length > 0) {
    structuredData.synergies = [];
    synergyResults.forEach(([queryId, result]) => {
      const queryType = queryTypeMap.get(queryId);

      logProcessor(`Processing synergy data for ${queryId}`, {
        queryType,
        dataStructure: result.data ? Object.keys(result.data) : null,
        hasNestedData: !!(result.data?.data)
      });

      if (queryType === 'synergy_analysis' || queryType === 'deck_improvement') {
        // Handle multi-card synergy analysis and deck improvement results
        if (result.data?.data) {
          Object.values(result.data.data).forEach((cardData: any) => {
            if (Array.isArray(cardData)) {
              cardData.forEach((card: any) => {
                if (card.synergizesWithCards) {
                  card.synergizesWithCards.forEach((synCard: any) => {
                    structuredData.synergies!.push({
                      card1: card.name,
                      card2: synCard.name,
                      description: `${card.name} synergizes with ${synCard.name}`
                    });
                  });
                }
              });
            }
          });
        }
      } else {
        // Handle single-card synergy lookup results
        if (result.data?.data?.cards) {
          result.data.data.cards.forEach((card: any) => {
            if (card.synergizesWithCards) {
              card.synergizesWithCards.forEach((synCard: any) => {
                structuredData.synergies!.push({
                  card1: card.name,
                  card2: synCard.name,
                  description: `${card.name} synergizes with ${synCard.name}`
                });
              });
            }
          });
        }
      }
    });
  }

  // Set metadata
  const successfulQueries = Object.values(queryResults).filter(r => r.success).length;
  const totalQueries = Object.keys(queryResults).length;

  if (successfulQueries === 0) {
    structuredData.confidence = 0.1;
    structuredData.dataQuality = 'low';
    structuredData.summary = 'No data could be retrieved';
  } else if (successfulQueries === totalQueries) {
    structuredData.confidence = 0.9;
    structuredData.dataQuality = 'high';
    structuredData.summary = 'All queries executed successfully';
  } else {
    structuredData.confidence = 0.6;
    structuredData.dataQuality = 'medium';
    structuredData.summary = 'Some queries executed successfully';
  }

  return structuredData;
}

/**
 * Legacy function - Process structured data through the Processor to generate a streaming user-facing response
 *
 * @param structuredData Data from the Orchestrator (OrchestratorPackage)
 * @param userMessage The original user message
 * @param chatHistory Previous messages in the conversation
 * @returns A streaming response for the user
 */
export async function streamWithProcessor(
  orchestratorData: any,
  userMessage: string,
  chatHistory: any[]
): Promise<ProcessorResponse> {
  try {
    logProcessor('Processing structured data from Orchestrator', {
      hasExecutableQueries: !!(orchestratorData && orchestratorData.executableQueries),
      hasUserContext: !!(orchestratorData && orchestratorData.userContext),
      hasDeckData: !!(orchestratorData && orchestratorData.deckData)
    });

    // Format chat history for context
    const formattedMessages = (chatHistory || []).map((msg: any) => ({
      role: msg.isUser ? 'user' as const : 'assistant' as const,
      content: msg.content || msg.text
    }));

    // Add the current user message
    formattedMessages.push({
      role: 'user' as const,
      content: userMessage
    });

    logProcessor('Formatted chat history for streaming Processor', {
      messageCount: formattedMessages.length,
      lastFewMessages: formattedMessages.slice(-3)
    });

    // Validate that we have the expected OrchestratorPackage structure
    if (!orchestratorData || !orchestratorData.executableQueries) {
      throw new Error('Invalid orchestrator data format - expected OrchestratorPackage');
    }

    const orchestratorPackage = orchestratorData as OrchestratorPackage;

    // Execute primary queries
    logProcessor('Executing primary queries for streaming', {
      queryCount: orchestratorPackage.executableQueries.primary.length,
      queryIds: orchestratorPackage.executableQueries.primary.map((q: ExecutableQuery) => q.id)
    });

    const primaryResults = await executeQueries(
      orchestratorPackage.executableQueries.primary,
      orchestratorPackage.apiConfig
    );

    logProcessor('Primary query results for streaming', {
      successfulQueries: Object.keys(primaryResults).filter(id => primaryResults[id].success),
      failedQueries: Object.keys(primaryResults).filter(id => !primaryResults[id].success)
    });

// Check if we actually have useful data (bypass the stupid analysis)
    const hasActualData = Object.values(primaryResults).some(result =>
      result.success && result.rowCount !== undefined && result.rowCount > 0
    );

    const dataSufficiencyCheck = {
      hasCriticalData: hasActualData, // If we have data, we have critical data!
      hasEnoughDetail: hasActualData,
      needsAdditionalQueries: false, // We're good with what we have
      missingElements: [],
      confidenceLevel: hasActualData ? 0.9 : 0.2, // High confidence if we have data
      recommendedAction: hasActualData ? 'synthesize' : 'partial_response'
    };

    logProcessor('Data Analysis Bypass (Structured)', {
      hasActualData,
      primaryResultsCount: Object.keys(primaryResults).length,
      successfulResults: Object.values(primaryResults).filter(r => r.success).length,
      resultsWithData: Object.values(primaryResults).filter(r => r.success && r.rowCount !== undefined && r.rowCount > 0).length,
      timestamp: new Date().toISOString()
    });

    logProcessor('Data sufficiency analysis for streaming', dataSufficiencyCheck);

    // Execute conditional queries if needed
    let conditionalResults: { [queryId: string]: QueryResult } = {};

    if (dataSufficiencyCheck.needsAdditionalQueries && orchestratorPackage.executableQueries.conditional.length > 0) {
      logProcessor('Executing conditional queries for streaming', {
        queryCount: orchestratorPackage.executableQueries.conditional.length,
        queryIds: orchestratorPackage.executableQueries.conditional.map((q: any) => q.id)
      });

      conditionalResults = await executeConditionalQueries(
        orchestratorPackage.executableQueries.conditional,
        primaryResults,
        orchestratorPackage.apiConfig
      );

      logProcessor('Conditional query results for streaming', {
        successfulQueries: Object.keys(conditionalResults).filter(id => conditionalResults[id].success),
        failedQueries: Object.keys(conditionalResults).filter(id => !conditionalResults[id].success)
      });
    }

    // Combine all query results
    const allQueryResults = {
      ...primaryResults,
      ...conditionalResults
    };

    // Log combined query results for processor (structured)
    logProcessor('Combined Query Results for Processor (Structured)', {
      total_queries: Object.keys(allQueryResults).length,
      successful_queries: Object.values(allQueryResults).filter(r => r.success).length,
      failed_queries: Object.values(allQueryResults).filter(r => !r.success).length,
      hasData: Object.values(allQueryResults).some(r => r.success && r.rowCount !== undefined && r.rowCount > 0),
      query_ids: Object.keys(allQueryResults),
      timestamp: new Date().toISOString()
    });

    // Create a concise summary for the processor message
    const dataSummary = summarizeQueryResults(allQueryResults);

    // Create a focused message for the processor requesting JSON output
    const processorMessage = {
      role: 'user' as const,
      content: `Please analyze this data and return a JSON response in the exact format specified in your system prompt.

User's message: "${userMessage}"

Intent: ${orchestratorPackage.userContext.intent}
Confidence: ${orchestratorPackage.userContext.confidence}

${orchestratorPackage.deckData ? `Deck Analysis:
- Format: ${orchestratorPackage.deckData.decoded?.formatName || 'Unknown'}
- Class: ${orchestratorPackage.deckData.decoded?.className || 'Unknown'}
- Total Cards: ${orchestratorPackage.deckData.decoded?.totalCards || 0}
- Valid: ${orchestratorPackage.deckData.isValid}
${orchestratorPackage.deckData.error ? `- Error: ${orchestratorPackage.deckData.error}` : ''}

` : ''}Query Results Summary:
${dataSummary}

Processing Instructions:
- Focus: ${orchestratorPackage.processingInstructions.primaryFocus}
- Style: ${orchestratorPackage.processingInstructions.responseStyle}
- Length: ${orchestratorPackage.processingInstructions.expectedLength}
- Include: ${orchestratorPackage.processingInstructions.includeElements.join(', ')}

Data Quality:
- Has critical data: ${dataSufficiencyCheck.hasCriticalData}
- Confidence: ${Math.round(dataSufficiencyCheck.confidenceLevel * 100)}%
- Recommended action: ${dataSufficiencyCheck.recommendedAction}
${dataSufficiencyCheck.missingElements.length > 0 ? `- Missing: ${dataSufficiencyCheck.missingElements.join(', ')}` : ''}

IMPORTANT: Return ONLY valid JSON in the format specified in your system prompt. Do not include any markdown formatting, explanatory text, or anything outside the JSON structure.`
    };

    logProcessor('Created focused processor message');

    // Log the exact message being sent to Claude (structured, PII-safe)
    logProcessor('Processor Message to Claude (PII-Safe)', {
      message_length: processorMessage.content.length,
      message_preview: processorMessage.content.slice(0, 200) + (processorMessage.content.length > 200 ? '...' : ''),
      contains_user_message: processorMessage.content.includes('User\'s message:'),
      contains_query_results: processorMessage.content.includes('Query Results Summary:'),
      contains_deck_analysis: processorMessage.content.includes('Deck Analysis:'),
      timestamp: new Date().toISOString()
    });

    // Call Claude Processor with structured output (no streaming)
    logProcessor('Calling Claude Processor for structured data synthesis');

    // PII-Safe logging of Claude #2 API call
    const redactedProcessorMessages = [...formattedMessages.slice(-5), processorMessage].map(msg => ({
      role: msg.role,
      content: msg.content
        .replace(/[A-Za-z0-9+/=]{20,150}/g, '[DECK_CODE_REDACTED]') // Redact deck codes
        .replace(/AAE[A-Za-z0-9+/=]+/g, '[HEARTHSTONE_DECK_CODE]') // Redact Hearthstone deck codes
        .replace(/"[^"]{50,}"/g, '"[LONG_USER_INPUT_REDACTED]"') // Redact long quoted strings
        .slice(0, 500) // Truncate to 500 chars for processor
    }));

    logProcessor('Claude #2 API Call (PII-Safe)', {
      system_prompt_length: PROCESSOR_SYSTEM_PROMPT.length,
      system_prompt_preview: PROCESSOR_SYSTEM_PROMPT.slice(0, 100) + '...',
      messages: redactedProcessorMessages,
      model: 'claude-3-5-sonnet-20241022',
      temperature: 0.7,
      max_tokens: 4096,
      stream: false,
      message_count: [...formattedMessages.slice(-5), processorMessage].length
    });

    const response = await callAnthropicWithRetry({
      model: 'claude-3-5-sonnet-20241022',
      system: PROCESSOR_SYSTEM_PROMPT,
      messages: [...formattedMessages.slice(-5), processorMessage], // Include limited context + processor message
      max_tokens: 4096,
      temperature: 0.7, // Higher temperature for more creative responses
      stream: false, // Disable streaming for structured output
    });

    logProcessor('Received response from Claude Processor');

    // Extract the content from Claude's response
    const rawContent = response.content[0] && 'text' in response.content[0]
      ? response.content[0].text
      : '';

    logProcessor('Raw Claude Processor response', { rawContent });

    // Parse the JSON response from Claude
    let parsedClaudeResponse: StructuredProcessorData;
    try {
      parsedClaudeResponse = JSON.parse(rawContent);
      logProcessor('Successfully parsed structured data from Claude Processor', parsedClaudeResponse);
    } catch (parseError) {
      // Enhanced JSON parse error logging
      logError('Processor', 'Failed to parse JSON from Claude Processor', {
        rawContent: rawContent,
        rawContentLength: rawContent.length,
        rawContentPreview: rawContent.slice(0, 300) + (rawContent.length > 300 ? '...' : ''),
        parseError: (parseError as Error).message,
        parseErrorType: (parseError as Error).name,
        fallback_action: 'Using default structured response',
        timestamp: new Date().toISOString()
      });

      // Fallback structured response
      parsedClaudeResponse = {
        decks: [],
        summary: "Unable to process deck data at this time",
        confidence: 0.1,
        dataQuality: "low",
        error: "JSON parsing failed"
      };

      logProcessor('Applied JSON parse fallback response', {
        fallback_response: parsedClaudeResponse,
        original_content_length: rawContent.length
      });
    }

    // Validate the structured data
    if (!parsedClaudeResponse.decks || !Array.isArray(parsedClaudeResponse.decks)) {
      logError('Processor', 'Claude Processor returned invalid deck structure', parsedClaudeResponse);
      parsedClaudeResponse.decks = [];
      parsedClaudeResponse.dataQuality = "low";
    }

    // Return structured data with metadata
    const processorResponse = {
      structuredResponse: parsedClaudeResponse,
      rawResponse: rawContent,
      queryResults: allQueryResults,
      metadata: {
        queriesExecuted: Object.keys(allQueryResults).length,
        successfulQueries: Object.values(allQueryResults).filter(r => r.success).length,
        hasData: Object.values(allQueryResults).some(r => r.success && r.rowCount !== undefined && r.rowCount > 0),
        confidence: parsedClaudeResponse.confidence || 0.5,
        dataQuality: parsedClaudeResponse.dataQuality || "medium"
      }
    };

    logProcessor('Created structured processor response', processorResponse);
    return processorResponse;

  } catch (error: any) {
    logError('Processor', 'Error in processor streaming', error);
    console.error('Error in processor streaming:', error);
    throw error;
  }
}

/**
 * Non-streaming processor function that returns structured data
 * Now delegates to streamWithProcessor which returns structured data instead of streams
 */
export async function processWithProcessor(
  orchestratorData: any,
  userMessage: string,
  chatHistory: any[]
): Promise<ProcessorResponse> {
  try {
    logProcessor('Non-streaming processor call - delegating to structured data version');

    // Use the structured data version (formerly streaming)
    const processorResponse = await streamWithProcessor(orchestratorData, userMessage, chatHistory);

    logProcessor('Received structured processor response', {
      hasStructuredResponse: !!(processorResponse && processorResponse.structuredResponse),
      deckCount: processorResponse?.structuredResponse?.decks?.length || 0,
      confidence: processorResponse?.metadata?.confidence || 0,
      dataQuality: processorResponse?.metadata?.dataQuality || 'unknown'
    });

    return processorResponse;

  } catch (error: any) {
    logError('Processor', 'Error in non-streaming processor', error);
    throw error;
  }
}
