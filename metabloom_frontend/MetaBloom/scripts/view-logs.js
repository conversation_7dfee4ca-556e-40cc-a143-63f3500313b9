/**
 * Simple script to view the Claude communication logs
 * 
 * Usage:
 * node scripts/view-logs.js [log-file]
 * 
 * Examples:
 * node scripts/view-logs.js                  # View the combined communication log
 * node scripts/view-logs.js orchestrator     # View the orchestrator log
 * node scripts/view-logs.js processor        # View the processor log
 */

const fs = require('fs');
const path = require('path');

// Define log file paths
const LOG_DIR = path.join(__dirname, '..', 'logs');
const ORCHESTRATOR_LOG = path.join(LOG_DIR, 'orchestrator.log');
const PROCESSOR_LOG = path.join(LOG_DIR, 'processor.log');
const COMMUNICATION_LOG = path.join(LOG_DIR, 'claude_communication.log');

// Get the log file to view
const logType = process.argv[2] || 'communication';

// Map log type to file path
const logFilePath = {
  'orchestrator': ORCHESTRATOR_LOG,
  'processor': PROCESSOR_LOG,
  'communication': COMMUNICATION_LOG
}[logType.toLowerCase()] || COMMUNICATION_LOG;

// Check if the log file exists
if (!fs.existsSync(logFilePath)) {
  console.error(`Log file not found: ${logFilePath}`);
  console.log('Available logs:');
  fs.readdirSync(LOG_DIR).forEach(file => {
    console.log(`- ${file}`);
  });
  process.exit(1);
}

// Read and display the log file
try {
  const logContent = fs.readFileSync(logFilePath, 'utf8');
  console.log(`=== ${path.basename(logFilePath)} ===\n`);
  console.log(logContent);
} catch (error) {
  console.error(`Error reading log file: ${error.message}`);
  process.exit(1);
}
