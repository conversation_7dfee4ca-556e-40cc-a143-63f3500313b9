/**
 * <PERSON> Orchestrator
 *
 * This module handles the orchestration layer of the Two-Claude Architecture.
 * It's responsible for:
 * 1. Analyzing user intent
 * 2. Generating search specifications (NOT actual queries)
 * 3. Detecting and processing deck codes
 * 4. Maintaining conversation context
 * 5. Delegating query execution to the Processor
 */

import Anthropic from '@anthropic-ai/sdk';
import { decode } from 'deckstrings';
import { logOrchestrator, logError, logUserMessage } from '@/lib/utils/claude<PERSON>ogger';
import {
  ExecutableQueries,
  OrchestratorPackage,
  UserContext,
  ProcessingInstructions,
  ApiConfig,
  ExecutableQuery
} from '@/lib/types/queryTypes';
import {
  validateAndDecodeDeck,
  generateDeckAnalysisQueries,
  generateErrorResponseQueries,
  ProcessedDeckData,
  DeckStats
} from '@/lib/utils/deckCodeUtils';

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

// Deck code regex pattern for detection
const DECK_CODE_REGEX = /^[A-Za-z0-9+/=]{20,150}$/;

/**
 * Orchestrator system prompt - focused ONLY on intent analysis
 * NO database operations - that's handled by the Processor
 */
const ORCHESTRATOR_SYSTEM_PROMPT = `You are the Orchestrator component.

## Your Responsibilities:
1. Analyze user intent and detect deck codes
2. Generate search specifications (NOT database queries)
3. Format structured output for the Processor

## Standard Values:
- Format names: "Standard", "Wild", "Classic", "Twist"
- Class names: "Demon Hunter", "Druid", "Hunter", "Mage", "Paladin", "Priest", "Rogue", "Shaman", "Warlock", "Warrior", "Death Knight", "Neutral"

## Guidelines:
- Focus ONLY on understanding what the user wants
- Generate search criteria, not database queries
- Output is only seen by the Processor component
- Structure responses in JSON format for the Processor

## Response Format:
Return JSON with: userContext, deckData (if applicable), searchSpecifications, processingInstructions, apiConfig.`;

/**
 * Search specification interface - what to search for, not how
 */
interface SearchSpecification {
  id: string;
  intent: 'find_cards' | 'find_decks' | 'find_archetypes' | 'find_synergies' | 'analyze_meta' | 'search_general';
  database: 'postgres' | 'vector' | 'graphql';
  criteria: {
    searchTerms?: string[];
    filters?: {
      className?: string;
      format?: string;
      manaCost?: number;
      rarity?: string;
      cardType?: string;
      deck_cards?: boolean;
      similarity_desc?: boolean;
      popularity_desc?: boolean;
      winrate_desc?: boolean;
    };
    limit?: number;
    sortBy?: string;
  };
  purpose: string;
  priority: 'critical' | 'important' | 'supplementary';
}

/**
 * Modified ExecutableQueries to use SearchSpecifications
 */
interface SearchSpecifications {
  primary: SearchSpecification[];
  conditional: Array<{
    condition: string;
    specification: SearchSpecification;
  }>;
}

/**
 * Process a user message through the Orchestrator
 *
 * @param message The user's message
 * @param chatHistory Previous messages in the conversation
 * @returns Structured data for the Processor
 */
export async function processWithOrchestrator(message: string, chatHistory: any[]) {
  try {
    // Log the incoming message
    logUserMessage(message);
    logOrchestrator('Processing user message', message);

    // Process deck code if present
    const deckCode = extractDeckCode(message);
    let processedDeckData: ProcessedDeckData | null = null;
    let deckIntent = false;

    if (deckCode) {
      // Process the deck code with enhanced validation
      processedDeckData = validateAndDecodeDeck(deckCode);
      logOrchestrator('Processed deck code', processedDeckData);

      // If the deck code is valid, set the intent to deck analysis
      if (processedDeckData.isValid) {
        deckIntent = true;
        logOrchestrator('Setting intent to deck analysis due to valid deck code');
      } else {
        // Log detailed error information for invalid deck codes
        logError('Orchestrator', 'Invalid deck code', {
          deckCode,
          error: processedDeckData.error,
          validation: processedDeckData.validation
        });
      }

      // Add deck statistics if available
      if (processedDeckData.isValid && processedDeckData.decoded) {
        const deckStats = analyzeDeckComposition(processedDeckData);
        logOrchestrator('Analyzed deck composition', deckStats);

        // Enrich the processed deck data with statistics
        processedDeckData.deckStats = deckStats;
      }
    }

    // Format chat history for Claude
    const formattedMessages = (chatHistory || []).map((msg: any) => ({
      role: msg.isUser ? 'user' : 'assistant',
      content: msg.content || msg.text
    }));

    // Add current message
    formattedMessages.push({
      role: 'user',
      content: message
    });

    logOrchestrator('Formatted chat history for Orchestrator', {
      messageCount: formattedMessages.length,
      lastFewMessages: formattedMessages.slice(-3)
    });

    // Call Claude Orchestrator
    logOrchestrator('Calling Claude Orchestrator');

    // PII-Safe logging of Claude #1 API call
    const redactedMessages = formattedMessages.map(msg => ({
      role: msg.role,
      content: msg.content
        .replace(/[A-Za-z0-9+/=]{20,150}/g, '[DECK_CODE_REDACTED]') // Redact deck codes
        .replace(/AAE[A-Za-z0-9+/=]+/g, '[HEARTHSTONE_DECK_CODE]') // Redact Hearthstone deck codes
        .slice(0, 200) // Truncate to 200 chars
    }));

    // Debug: Verify system prompt is available
    logOrchestrator('System Prompt Debug', {
      prompt_defined: !!ORCHESTRATOR_SYSTEM_PROMPT,
      prompt_length: ORCHESTRATOR_SYSTEM_PROMPT?.length || 0,
      prompt_type: typeof ORCHESTRATOR_SYSTEM_PROMPT
    });

    logOrchestrator('Claude #1 API Call (PII-Safe)', {
      system_prompt_length: ORCHESTRATOR_SYSTEM_PROMPT.length,
      system_prompt_preview: ORCHESTRATOR_SYSTEM_PROMPT.slice(0, 100) + '...',
      messages: redactedMessages,
      model: 'claude-3-5-sonnet-20241022',
      temperature: 0.2,
      max_tokens: 4096,
      message_count: formattedMessages.length,
      timestamp: new Date().toISOString()
    });

    const response = await anthropic.messages.create({
      model: 'claude-3-5-sonnet-20241022',
      system: ORCHESTRATOR_SYSTEM_PROMPT,
      messages: formattedMessages.map(msg => ({
        role: msg.role === 'user' ? 'user' as const : 'assistant' as const,
        content: msg.content
      })),
      max_tokens: 4096,
      temperature: 0.2, // Lower temperature for more deterministic intent analysis
    });

    // Parse the orchestrator's response
    const orchestratorOutput = response.content[0] && 'text' in response.content[0]
      ? response.content[0].text
      : '';
    logOrchestrator('Received response from Claude Orchestrator', orchestratorOutput);

    // Try to parse as JSON to get the intent
    let intent = 'general_query';
    let confidence = 0.5;

    try {
      const parsedOutput = JSON.parse(orchestratorOutput);
      if (parsedOutput.userContext && parsedOutput.userContext.intent) {
        intent = parsedOutput.userContext.intent;
        confidence = parsedOutput.userContext.confidence || 0.5;
      } else if (parsedOutput.intent) {
        intent = parsedOutput.intent;
        confidence = parsedOutput.confidence || 0.5;
      }
    } catch (error) {
      // If we can't parse the output, extract intent using regex
      const intentMatch = orchestratorOutput.match(/"intent":\s*"([^"]+)"/);
      if (intentMatch && intentMatch[1]) {
        intent = intentMatch[1];
      }
      logOrchestrator('Extracted intent from output', { intent });
    }

    // Override intent if a valid deck code was detected
    if (deckIntent) {
      const originalIntent = intent;
      intent = 'deck_analysis';
      confidence = 0.95; // High confidence for deck code detection
      logOrchestrator('Overriding intent due to valid deck code', {
        originalIntent,
        newIntent: intent,
        confidence
      });
    }

    // Generate search specifications based on intent and deck data
    const searchSpecifications = generateSearchSpecificationsBasedOnIntent(intent, message, processedDeckData);
    logOrchestrator('Generated search specifications', searchSpecifications);

    // Convert search specifications to executable queries for backward compatibility
    const executableQueries = convertSpecificationsToQueries(searchSpecifications);

    // Generate the OrchestratorPackage
    const orchestratorPackage = generateOrchestratorPackage(
      message,
      intent,
      confidence,
      chatHistory,
      processedDeckData,
      executableQueries
    );

    logOrchestrator('Created OrchestratorPackage for processor', orchestratorPackage);
    return {
      type: 'structured_data',
      data: orchestratorPackage
    };
  } catch (error: any) {
    logError('Orchestrator', 'Error in orchestrator', error);
    console.error('Error in orchestrator:', error);

    // Generate a fallback package with general search specifications
    const fallbackSpecifications = generateGeneralSearchSpecifications(message);
    const fallbackQueries = convertSpecificationsToQueries(fallbackSpecifications);
    const fallbackPackage = generateOrchestratorPackage(
      message,
      'general_query',
      0.3,
      chatHistory,
      null,
      fallbackQueries
    );

    logOrchestrator('Created fallback OrchestratorPackage due to error', fallbackPackage);
    return {
      type: 'structured_data',
      data: fallbackPackage
    };
  }
}

/**
 * Generate search specifications based on intent (NOT actual queries)
 */
// In your orchestrator.ts, update this function:
function generateSearchSpecificationsBasedOnIntent(
  intent: string,
  message: string,
  deckData: ProcessedDeckData | null
): SearchSpecifications {
  // Log function input with PII-safe message
  logOrchestrator('generateSearchSpecificationsBasedOnIntent - Input', {
    intent: intent,
    message_preview: message.slice(0, 100) + (message.length > 100 ? '...' : ''),
    message_length: message.length,
    has_deck_data: !!deckData,
    deck_data_valid: deckData?.isValid || false,
    timestamp: new Date().toISOString()
  });

  // If we have valid deck data, prioritize deck-related specifications
  if (deckData && deckData.isValid) {
    logOrchestrator('Using deck analysis specifications (valid deck data)');
    return generateDeckAnalysisSpecifications(deckData);
  }

  // If we have invalid deck data, generate error response specifications
  if (deckData && !deckData.isValid) {
    logOrchestrator('Using error response specifications (invalid deck data)');
    return generateErrorResponseSpecifications(deckData);
  }

  // IMPROVED INTENT MAPPING - map multiple intents to deck requests
  const lowerMessage = message.toLowerCase();

  // Check if this is actually a deck request regardless of detected intent
  if (lowerMessage.includes('deck') ||
      lowerMessage.includes('best') && (lowerMessage.includes('mage') || lowerMessage.includes('warrior') || lowerMessage.includes('priest') || lowerMessage.includes('paladin') || lowerMessage.includes('hunter') || lowerMessage.includes('druid') || lowerMessage.includes('rogue') || lowerMessage.includes('shaman') || lowerMessage.includes('warlock') || lowerMessage.includes('demon hunter') || lowerMessage.includes('death knight'))) {
    logOrchestrator('Intent Override: Detected deck request', {
      original_intent: intent,
      override_reason: 'Message contains deck keywords',
      keywords_found: lowerMessage.includes('deck') ? ['deck'] : ['best', 'class_name'],
      timestamp: new Date().toISOString()
    });
    const result = generateDeckRequestSpecifications(message);
    logOrchestrator('generateSearchSpecificationsBasedOnIntent - Output (Intent Override)', {
      specifications_type: 'deck_request',
      primary_count: result.primary.length,
      conditional_count: result.conditional.length
    });
    return result;
  }

  // Otherwise, use the original intent detection
  let result: SearchSpecifications;
  switch (intent.toLowerCase()) {
    case 'card_inquiry':
      result = generateCardInquirySpecifications(message);
      break;
    case 'meta_analysis':
      result = generateMetaAnalysisSpecifications(message);
      break;
    case 'strategy_question':
      result = generateStrategySpecifications(message);
      break;
    case 'deck_request':
    case 'find_best_deck':
    case 'find_best_performing_deck':  // Add this mapping
      result = generateDeckRequestSpecifications(message);
      break;
    default:
      result = generateGeneralSearchSpecifications(message);
  }

  // Log function output
  logOrchestrator('generateSearchSpecificationsBasedOnIntent - Output', {
    intent_used: intent.toLowerCase(),
    specifications_type: intent.toLowerCase(),
    primary_count: result.primary.length,
    conditional_count: result.conditional.length,
    primary_query_ids: result.primary.map(q => q.id),
    conditional_query_ids: result.conditional.map(q => q.specification.id)
  });

  return result;
}

/**
 * Generate specifications for card inquiries
 */
function generateCardInquirySpecifications(message: string): SearchSpecifications {
  const cardName = extractCardName(message);
  const className = extractClass(message);

  return {
    primary: [
      {
        id: "card_search",
        intent: "find_cards",
        database: "postgres",
        criteria: {
          searchTerms: [cardName || message.substring(0, 50)],
          filters: className ? { className } : {},
          limit: 10
        },
        purpose: "Find cards matching the user's description",
        priority: "critical"
      },
      {
        id: "card_vector_search",
        intent: "search_general",
        database: "postgres", // Changed from "vector" to "postgres" for pgvector queries
        criteria: {
          searchTerms: [message.substring(0, 100)],
          limit: 5
        },
        purpose: "Find semantically similar cards using pgvector",
        priority: "important"
      }
    ],
    conditional: [
      {
        condition: "if_card_found",
        specification: {
          id: "card_synergies",
          intent: "find_synergies",
          database: "graphql",
          criteria: {
            searchTerms: [cardName || ""],
            limit: 5
          },
          purpose: "Find cards that synergize with the requested card",
          priority: "important"
        }
      }
    ]
  };
}

/**
 * Generate specifications for meta analysis
 */
function generateMetaAnalysisSpecifications(message: string): SearchSpecifications {
  const format = extractFormat(message) || 'Standard';

  return {
    primary: [
      {
        id: "meta_decks",
        intent: "find_decks",
        database: "postgres",
        criteria: {
          filters: { format },
          limit: 10,
          sortBy: "winrate_desc"
        },
        purpose: "Get top performing decks in the current meta",
        priority: "critical"
      }
    ],
    conditional: []
  };
}

/**
 * Generate specifications for strategy questions
 */
function generateStrategySpecifications(message: string): SearchSpecifications {
  const strategyKeywords = extractStrategyKeywords(message);

  return {
    primary: [
      {
        id: "strategy_concepts",
        intent: "search_general",
        database: "graphql",
        criteria: {
          searchTerms: strategyKeywords,
          limit: 5
        },
        purpose: "Find strategy concepts related to the query",
        priority: "important"
      },
      {
        id: "strategy_decks",
        intent: "find_decks",
        database: "postgres",
        criteria: {
          filters: { format: "Standard" },
          limit: 5,
          sortBy: "winrate_desc"
        },
        purpose: "Find top performing decks for strategy analysis",
        priority: "important"
      }
    ],
    conditional: []
  };
}

/**
 * Generate specifications for deck requests
 */
function generateDeckRequestSpecifications(message: string): SearchSpecifications {
  const className = extractClass(message);
  const formatName = extractFormat(message) || 'Standard';

  return {
    primary: [
      {
        id: "recommended_decks",
        intent: "find_decks",
        database: "postgres",
        criteria: {
          filters: { className, format: formatName },
          limit: 5,
          sortBy: "winrate_desc"
        },
        purpose: "Find recommended decks for the requested class and format",
        priority: "critical"
      }
    ],
    conditional: []
  };
}

/**
 * Generate general search specifications
 */
function generateGeneralSearchSpecifications(message: string): SearchSpecifications {
  return {
    primary: [
      {
        id: "general_search",
        intent: "search_general",
        database: "postgres", // Changed from "vector" to "postgres" since vector search uses pgvector in PostgreSQL
        criteria: {
          searchTerms: [message.substring(0, 100)],
          limit: 10
        },
        purpose: "General search for user query",
        priority: "important"
      }
    ],
    conditional: []
  };
}

/**
 * Generate specifications for deck analysis
 */
function generateDeckAnalysisSpecifications(deckData: ProcessedDeckData): SearchSpecifications {
  if (!deckData.isValid || !deckData.decoded) {
    return { primary: [], conditional: [] };
  }

  return {
    primary: [
      {
        id: "deck_card_details",
        intent: "find_cards",
        database: "postgres",
        criteria: {
          searchTerms: [], // Will be populated with card IDs by processor
          filters: { deck_cards: true },
          limit: 50
        },
        purpose: "Get complete information for all cards in the deck",
        priority: "critical"
      },
      {
        id: "deck_synergies",
        intent: "find_synergies",
        database: "graphql",
        criteria: {
          searchTerms: [], // Will be populated with card names by processor
          limit: 10
        },
        purpose: "Analyze synergies between deck cards",
        priority: "important"
      }
    ],
    conditional: [
      {
        condition: "if_card_details_successful",
        specification: {
          id: "similar_decks",
          intent: "find_decks",
          database: "postgres",
          criteria: {
            filters: {
              format: deckData.decoded.formatName,
              className: deckData.decoded.className
            },
            limit: 5,
            sortBy: "similarity_desc"
          },
          purpose: "Find similar meta decks for comparison",
          priority: "important"
        }
      }
    ]
  };
}

/**
 * Generate specifications for error responses
 */
function generateErrorResponseSpecifications(deckData: ProcessedDeckData): SearchSpecifications {
  return {
    primary: [
      {
        id: "popular_decks",
        intent: "find_decks",
        database: "postgres",
        criteria: {
          limit: 5,
          sortBy: "popularity_desc"
        },
        purpose: "Provide alternative deck suggestions",
        priority: "important"
      }
    ],
    conditional: []
  };
}

/**
 * Convert search specifications to executable queries for backward compatibility
 * The Processor will use getInstructionKit to convert these to actual SQL
 */
function convertSpecificationsToQueries(specifications: SearchSpecifications): ExecutableQueries {
  const primary: ExecutableQuery[] = specifications.primary.map(spec => ({
    id: spec.id,
    database: spec.database,
    query: `SPECIFICATION:${JSON.stringify(spec)}`, // Special format for processor
    params: [spec.criteria],
    purpose: spec.purpose,
    priority: spec.priority
  }));

  const conditional = specifications.conditional.map(condSpec => ({
    condition: condSpec.condition,
    id: condSpec.specification.id,
    database: condSpec.specification.database,
    query: `SPECIFICATION:${JSON.stringify(condSpec.specification)}`,
    params: [condSpec.specification.criteria],
    purpose: condSpec.specification.purpose
  }));

  return { primary, conditional };
}

/**
 * Generate a complete OrchestratorPackage for the Processor
 */
function generateOrchestratorPackage(
  message: string,
  intent: string,
  confidence: number,
  chatHistory: any[],
  deckData: ProcessedDeckData | null,
  executableQueries: ExecutableQueries
): OrchestratorPackage {
  // Log function input with PII-safe message
  logOrchestrator('generateOrchestratorPackage - Input', {
    message_preview: message.slice(0, 100) + (message.length > 100 ? '...' : ''),
    message_length: message.length,
    intent: intent,
    confidence: confidence,
    chat_history_length: chatHistory.length,
    has_deck_data: !!deckData,
    deck_data_valid: deckData?.isValid || false,
    primary_queries_count: executableQueries.primary.length,
    conditional_queries_count: executableQueries.conditional.length,
    timestamp: new Date().toISOString()
  });

  // Create the user context
  const userContext: UserContext = {
    originalMessage: message,
    intent: intent,
    confidence: confidence,
    conversationHistory: chatHistory
  };

  // Determine processing instructions based on intent and message
  const processingInstructions = determineProcessingInstructions(intent, message, deckData);

  // Create the API configuration
  const apiConfig: ApiConfig = {
    endpoint: "https://wa6kt26wi1.execute-api.us-east-1.amazonaws.com/prod/query",
    timeout: 30000,
    retryAttempts: 2
  };

  // Create the complete OrchestratorPackage
  const orchestratorPackage = {
    userContext,
    deckData,
    executableQueries,
    processingInstructions,
    apiConfig
  };

  // Log function output
  logOrchestrator('generateOrchestratorPackage - Output', {
    package_created: true,
    api_endpoint: apiConfig.endpoint,
    processing_focus: processingInstructions.primaryFocus,
    processing_style: processingInstructions.responseStyle,
    processing_length: processingInstructions.expectedLength,
    include_elements_count: processingInstructions.includeElements.length,
    timestamp: new Date().toISOString()
  });

  return orchestratorPackage;
}

/**
 * Determine processing instructions based on intent and message
 */
function determineProcessingInstructions(
  intent: string,
  message: string,
  _deckData: ProcessedDeckData | null
): ProcessingInstructions {
  // Default instructions
  const defaultInstructions: ProcessingInstructions = {
    primaryFocus: "Answer the user's question based on available data",
    responseStyle: "casual",
    expectedLength: "moderate",
    includeElements: ["direct_answer", "related_information"]
  };

  // Customize based on intent
  switch (intent.toLowerCase()) {
    case 'deck_analysis':
      return {
        primaryFocus: "Comprehensive deck analysis with strengths, weaknesses, and meta positioning",
        responseStyle: "analytical",
        expectedLength: "comprehensive",
        includeElements: ["deck_archetype", "mana_curve", "synergies", "meta_comparison", "improvement_suggestions"]
      };

    case 'card_inquiry':
      return {
        primaryFocus: "Detailed card information and usage guidance",
        responseStyle: "analytical",
        expectedLength: "moderate",
        includeElements: ["card_stats", "card_text", "common_uses", "synergies", "alternatives"]
      };

    case 'meta_analysis':
      return {
        primaryFocus: "Current meta trends and top performing decks",
        responseStyle: "analytical",
        expectedLength: "comprehensive",
        includeElements: ["tier_list", "class_performance", "meta_trends", "counter_strategies"]
      };

    case 'strategy_question':
      return {
        primaryFocus: "Strategic advice and gameplay concepts",
        responseStyle: message.toLowerCase().includes('beginner') ? "beginner_friendly" : "expert",
        expectedLength: "comprehensive",
        includeElements: ["concept_explanation", "practical_examples", "common_mistakes", "improvement_tips"]
      };

    case 'deck_request':
      return {
        primaryFocus: "Deck recommendation with explanation and usage guide",
        responseStyle: "casual",
        expectedLength: "comprehensive",
        includeElements: ["deck_code", "card_choices", "mulligan_guide", "matchup_advice", "play_strategy"]
      };

    default:
      return defaultInstructions;
  }
}

/**
 * Extract a potential deck code from a message
 */
function extractDeckCode(message: string): string | null {
  // Look for deck codes in the message
  const lines = message.split('\n');

  for (const line of lines) {
    const trimmed = line.trim();
    // Check if this line matches our deck code pattern
    if (DECK_CODE_REGEX.test(trimmed)) {
      return trimmed;
    }
  }

  return null;
}

/**
 * Validate a Hearthstone deck code
 */
export function validateDeckCode(deckCode: string): boolean {
  try {
    // Attempt to decode the deck code
    const decoded = decode(deckCode);

    // Basic validation - a valid deck should have:
    // - A format (heroes[0])
    // - At least some cards
    return decoded &&
           Array.isArray(decoded.heroes) &&
           decoded.heroes.length > 0 &&
           Array.isArray(decoded.cards) &&
           decoded.cards.length > 0;
  } catch (error) {
    console.error('Error validating deck code:', error);
    return false;
  }
}

/**
 * Helper function to extract a card name from a message
 */
function extractCardName(message: string): string {
  // Improved card name extraction
  const cardNameMatch = message.match(/(?:card|minion|spell|weapon|hero|secret|location)\s+(?:called|named)?\s*["']?([A-Za-z\s',\-]+)["']?/i);
  if (cardNameMatch) {
    return cardNameMatch[1].trim();
  }

  // Look for quoted strings that might be card names
  const quotedMatch = message.match(/["']([A-Za-z\s',\-]{3,30})["']/);
  if (quotedMatch) {
    return quotedMatch[1].trim();
  }

  return '';
}

/**
 * Helper function to extract a format from a message
 */
function extractFormat(message: string): string | null {
  const formats = ['Standard', 'Wild', 'Classic', 'Twist'];
  const lowerMessage = message.toLowerCase();

  for (const format of formats) {
    if (lowerMessage.includes(format.toLowerCase())) {
      return format;
    }
  }

  return null;
}

/**
 * Helper function to extract a class from a message
 */
function extractClass(message: string): string {
  const classes = ['Mage', 'Warrior', 'Warlock', 'Priest', 'Paladin', 'Hunter', 'Druid', 'Rogue', 'Shaman', 'Demon Hunter', 'Death Knight'];
  const lowerMessage = message.toLowerCase();

  for (const className of classes) {
    if (lowerMessage.includes(className.toLowerCase())) {
      return className;
    }
  }

  return ''; // Return empty string instead of default
}

/**
 * Helper function to extract strategy keywords from a message
 */
function extractStrategyKeywords(message: string): string[] {
  const strategyKeywords = [
    'tempo', 'value', 'control', 'aggro', 'midrange', 'combo',
    'mulligan', 'curve', 'trade', 'face', 'lethal', 'board control'
  ];

  const foundKeywords = [];
  const lowerMessage = message.toLowerCase();

  for (const keyword of strategyKeywords) {
    if (lowerMessage.includes(keyword.toLowerCase())) {
      foundKeywords.push(keyword);
    }
  }

  return foundKeywords.length > 0 ? foundKeywords : ['general strategy'];
}

/**
 * Analyze deck composition to generate statistics
 */
function analyzeDeckComposition(processedDeck: ProcessedDeckData): DeckStats {
  if (!processedDeck.isValid || !processedDeck.decoded) {
    throw new Error('Cannot analyze invalid deck');
  }

  const { cards } = processedDeck.decoded;

  // Initialize statistics
  const manaCurve: { [manaCost: number]: number } = {};
  const cardTypes = {
    minions: 0,
    spells: 0,
    weapons: 0,
    locations: 0,
    heroes: 0,
    other: 0
  };
  const rarityDistribution = {
    common: 0,
    rare: 0,
    epic: 0,
    legendary: 0,
    free: 0
  };
  const keywords: { [keyword: string]: number } = {};

  // Mock card database for demonstration
  // In a real implementation, this would be fetched from the database
  const mockCardDatabase: { [cardId: number]: any } = {};

  // Populate mock card database with some example cards
  cards.forEach(([cardId]) => {
    if (!mockCardDatabase[cardId]) {
      // Generate mock card data based on card ID
      // This is just for demonstration purposes
      const manaCost = Math.min(10, Math.floor(cardId % 11));
      const isLegendary = cardId % 4 === 0;
      const isEpic = !isLegendary && cardId % 3 === 0;
      const isRare = !isLegendary && !isEpic && cardId % 2 === 0;

      const cardTypes = ['minion', 'spell', 'weapon', 'location', 'hero'];
      const cardType = cardTypes[cardId % 5];

      const possibleKeywords = ['Battlecry', 'Deathrattle', 'Taunt', 'Divine Shield', 'Rush', 'Charge', 'Discover'];
      const keywordCount = Math.floor(cardId % 3);
      const cardKeywords: string[] = [];

      for (let i = 0; i < keywordCount; i++) {
        const keyword = possibleKeywords[cardId % possibleKeywords.length];
        if (!cardKeywords.includes(keyword)) {
          cardKeywords.push(keyword);
        }
      }

      mockCardDatabase[cardId] = {
        id: cardId,
        manaCost,
        type: cardType,
        rarity: isLegendary ? 'legendary' : (isEpic ? 'epic' : (isRare ? 'rare' : 'common')),
        keywords: cardKeywords,
        dustCost: isLegendary ? 1600 : (isEpic ? 400 : (isRare ? 100 : 40))
      };
    }
  });

  // Calculate statistics
  let totalManaCost = 0;
  let totalDustCost = 0;

  cards.forEach(([cardId, count]) => {
    const card = mockCardDatabase[cardId];

    // Mana curve
    if (!manaCurve[card.manaCost]) {
      manaCurve[card.manaCost] = 0;
    }
    manaCurve[card.manaCost] += count;

    // Card types
    switch (card.type) {
      case 'minion':
        cardTypes.minions += count;
        break;
      case 'spell':
        cardTypes.spells += count;
        break;
      case 'weapon':
        cardTypes.weapons += count;
        break;
      case 'location':
        cardTypes.locations += count;
        break;
      case 'hero':
        cardTypes.heroes += count;
        break;
      default:
        cardTypes.other += count;
    }

    // Rarity distribution
    switch (card.rarity) {
      case 'common':
        rarityDistribution.common += count;
        break;
      case 'rare':
        rarityDistribution.rare += count;
        break;
      case 'epic':
        rarityDistribution.epic += count;
        break;
      case 'legendary':
        rarityDistribution.legendary += count;
        break;
      default:
        rarityDistribution.free += count;
    }

    // Keywords
    if (card.keywords) {
      card.keywords.forEach((keyword: string) => {
        if (!keywords[keyword]) {
          keywords[keyword] = 0;
        }
        keywords[keyword] += count;
      });
    }

    // Total mana cost
    totalManaCost += card.manaCost * count;

    // Dust cost
    totalDustCost += card.dustCost * count;
  });

  // Calculate average mana cost
  const averageManaCost = totalManaCost / processedDeck.decoded.totalCards;

  return {
    manaCurve,
    cardTypes,
    rarityDistribution,
    keywords,
    averageManaCost,
    estimatedDustCost: totalDustCost
  };
}