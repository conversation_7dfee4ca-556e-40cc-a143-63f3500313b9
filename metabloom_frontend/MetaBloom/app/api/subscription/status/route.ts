import { NextRequest, NextResponse } from 'next/server';
import { getUserProfile, hasActiveSubscription, syncSubscriptionWithStripe } from '@/lib/subscription';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get user profile from Firestore
    const userProfile = await getUserProfile(userId);
    
    if (!userProfile) {
      return NextResponse.json({
        hasActiveSubscription: false,
        currentPlan: 'free',
        subscription: null,
      });
    }

    // Sync with <PERSON>e to get latest status
    const syncedSubscription = await syncSubscriptionWithStripe(userId);
    const subscription = syncedSubscription || userProfile.subscription;

    // Check if user has active subscription
    const hasActive = await hasActiveSubscription(userId);

    return NextResponse.json({
      hasActiveSubscription: hasActive,
      currentPlan: subscription.planType,
      subscription: {
        planType: subscription.planType,
        status: subscription.status,
        currentPeriodEnd: subscription.currentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        isYearly: subscription.isYearly,
      },
      tokenUsage: userProfile.tokenUsage,
    });
  } catch (error) {
    console.error('Error fetching subscription status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId, planType } = await request.json();

    if (!userId || !planType) {
      return NextResponse.json(
        { error: 'User ID and plan type are required' },
        { status: 400 }
      );
    }

    // Check if user already has this plan
    const hasActivePlan = await hasActiveSubscription(userId, planType);
    
    if (hasActivePlan) {
      return NextResponse.json(
        { 
          canSubscribe: false, 
          error: `You already have an active ${planType} subscription` 
        },
        { status: 409 }
      );
    }

    // Check if user has any active subscription
    const hasAnyActive = await hasActiveSubscription(userId);
    
    return NextResponse.json({
      canSubscribe: true,
      hasExistingSubscription: hasAnyActive,
      message: hasAnyActive 
        ? 'You have an existing subscription. Proceeding will upgrade/downgrade your plan.'
        : 'You can subscribe to this plan.',
    });
  } catch (error) {
    console.error('Error checking subscription eligibility:', error);
    return NextResponse.json(
      { error: 'Failed to check subscription eligibility' },
      { status: 500 }
    );
  }
}
