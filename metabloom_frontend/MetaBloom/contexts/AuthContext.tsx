"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  User,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  signInWithPopup,
  UserCredential,
  sendSignInLinkToEmail,
  isSignInWithEmailLink,
  signInWithEmailLink
} from 'firebase/auth';
import { auth, googleProvider, actionCodeSettings } from '../lib/firebase';
import { useAuth as useAuthStore } from '@/stores/auth';

interface AuthContextType {
  currentUser: User | null;
  loading: boolean;
  signup: (email: string, password: string) => Promise<UserCredential>;
  login: (email: string, password: string) => Promise<UserCredential>;
  logout: () => Promise<void>;
  loginWithGoogle: () => Promise<UserCredential>;
  sendSignInLink: (email: string) => Promise<void>;
  confirmSignInWithEmailLink: (email: string, href: string) => Promise<UserCredential>;
  isSignInWithEmailLink: (href: string) => boolean;
}

const AuthContext = createContext<AuthContextType>({} as AuthContextType);

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { setUser } = useAuthStore();

  function signup(email: string, password: string) {
    return createUserWithEmailAndPassword(auth, email, password);
  }

  function login(email: string, password: string) {
    return signInWithEmailAndPassword(auth, email, password);
  }

  function loginWithGoogle() {
    return signInWithPopup(auth, googleProvider);
  }

  function logout() {
    return signOut(auth);
  }

  function sendSignInLink(email: string) {
    // Save the email locally so you don't need to ask the user for it again
    // if they open the link on the same device
    localStorage.setItem('emailForSignIn', email);
    return sendSignInLinkToEmail(auth, email, actionCodeSettings);
  }

  function confirmSignInWithEmailLink(email: string, href: string) {
    return signInWithEmailLink(auth, email, href);
  }

  function checkIsSignInWithEmailLink(href: string) {
    return isSignInWithEmailLink(auth, href);
  }

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);
      setUser(user);

      // Initialize user profile in Firestore when user signs in
      if (user) {
        try {
          await fetch('/api/user/initialize', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: user.uid,
              email: user.email,
              displayName: user.displayName,
            }),
          });
        } catch (error) {
          console.error('Error initializing user profile:', error);
        }
      }

      setLoading(false);
    });

    return unsubscribe;
  }, [setUser]);

  const value = {
    currentUser,
    loading,
    signup,
    login,
    logout,
    loginWithGoogle,
    sendSignInLink,
    confirmSignInWithEmailLink,
    isSignInWithEmailLink: checkIsSignInWithEmailLink
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}
