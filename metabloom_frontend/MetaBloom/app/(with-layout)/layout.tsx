"use client";
import Footer from "@/components/Footer";
import Header from "@/components/Header";
import MainSidebar from "@/components/MainSidebar";
import MainModal from "@/components/modals/MainModal";
import GradientBackground from "@/components/ui/GradientBackground";
//import Banner from "@/components/ui/Banner";
import { useChatHandler } from "@/stores/chatList";
import { useAuth } from "@/stores/auth";
import { usePathname } from "next/navigation";
import React, { useEffect, useState } from "react";

function Layout({ children }: { children: React.ReactNode }) {
  const [showSidebar, setShowSidebar] = useState(false);
  const { updateChatList } = useChatHandler();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    updateChatList();
  }, []);

  // Reset sidebar state when authentication status changes
  useEffect(() => {
    if (!isAuthenticated) {
      setShowSidebar(false);
    }
  }, [isAuthenticated]);

  return (
    <div className="text-n500 bg-white relative z-10 h-dvh overflow-hidden dark:bg-n0 dark:text-n30">
      <GradientBackground />
      <div className="flex justify-start items-start h-full ">
        {/* Only render sidebar when user is authenticated */}
        {isAuthenticated && (
          <MainSidebar
            showSidebar={showSidebar}
            setShowSidebar={setShowSidebar}
          />
        )}
        <div className={`flex-1 flex flex-col justify-between items-center h-full pb-3 relative z-20 overflow-hidden ${!isAuthenticated ? 'w-full' : ''}`}>
          <div className="w-full flex flex-col">
            <Header showSidebar={showSidebar} setShowSidebar={setShowSidebar} />
            {/* Only show banner when user is not authenticated */}
            {!isAuthenticated && (

              <div className="w-full" style={{ marginTop: '0', padding: '0' }}>


              </div>
            )}
          </div>
          {children}
          <Footer />
        </div>
      </div>



      {/* Modal */}
      <MainModal />
    </div>
  );
}

export default Layout;
