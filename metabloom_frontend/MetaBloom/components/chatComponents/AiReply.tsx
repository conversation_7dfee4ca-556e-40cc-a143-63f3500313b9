import React, { useState } from "react";
import Image from "next/image";
import logo from "@/public/images/MetaBloom_logo.png"; // Using the MetaBloom logo for HearthForge
import {
  PiArrowsCounterClockwise,
  PiCopy,
  PiShareFat,
  PiThumbsDown,
  PiThum<PERSON>Up,
  PiCheck,
} from "react-icons/pi";
import "@/styles/typing-indicator.css";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";
import rehypeHighlight from "rehype-highlight";
import remarkGfm from "remark-gfm";
import DeckCode from "./DeckCode";

type AiReplyProps = {
  replyText: string;
  replyTime?: string;
  isStreaming?: boolean;
};

function AiReply({ replyText, replyTime = "just now", isStreaming = false }: AiReplyProps) {
  const [copied, setCopied] = useState(false);
  const [codeCopied, setCodeCopied] = useState(false);

  // Don't render anything if there's no text and we're not streaming
  if (!replyText && !isStreaming) {
    return null;
  }

  // Function to copy text to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(replyText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Function to copy code to clipboard
  const copyCodeToClipboard = (code: string) => {
    navigator.clipboard.writeText(code);
    setCodeCopied(true);
    setTimeout(() => setCodeCopied(false), 2000);
  };

  return (
    <div className="flex justify-start items-start gap-1 sm:gap-3 w-full">
      <Image src={logo} alt="HearthForge Logo" width={32} height={32} className="max-sm:size-5 object-cover" />
      <div className="flex flex-col justify-start items-start gap-3 flex-1 max-w-full">
        <p className="text-xs text-n100">HearthForge, {replyTime}</p>
        <div className="text-sm bg-primaryColor/5 py-3 px-5 border border-primaryColor/20 rounded-lg w-full markdown-content overflow-hidden">
          {isStreaming && !replyText ? (
            // Only show typing indicator when streaming starts and no text yet
            <div className="flex items-center">
              <span className="typing-dot"></span>
              <span className="typing-dot"></span>
              <span className="typing-dot"></span>
            </div>
          ) : (
            // Display the actual AI response with markdown formatting
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw, rehypeHighlight]}
              components={{
                // Custom rendering for code blocks
                code({ node, inline, className, children, ...props }) {
                  const match = /language-(\w+)/.exec(className || '');
                  const codeContent = String(children).replace(/\n$/, '');

                  // Check if this is a Hearthstone deck code (typically no language specified and in a specific format)
                  const isDeckCode = !match &&
                    codeContent.trim().length > 20 &&
                    codeContent.trim().length < 150 &&
                    /^[A-Za-z0-9+/=]+$/.test(codeContent.trim());

                  if (isDeckCode) {
                    return <DeckCode code={codeContent.trim()} />;
                  }

                  return !inline ? (
                    <div className="relative w-full">
                      <pre className={`${match ? `language-${match[1]}` : ''} rounded-md p-4 bg-gray-800 text-white overflow-hidden`}>
                        <code className={`${className} whitespace-pre-wrap break-all`} {...props}>
                          {children}
                        </code>
                      </pre>
                      <button
                        onClick={() => copyCodeToClipboard(codeContent)}
                        className="absolute top-2 right-2 p-1 rounded-md bg-gray-700 text-white hover:bg-gray-600"
                        title="Copy code"
                      >
                        {codeCopied ? <PiCheck /> : <PiCopy />}
                      </button>
                    </div>
                  ) : (
                    <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded-md" {...props}>
                      {children}
                    </code>
                  );
                },
                // Custom rendering for tables
                table({ node, ...props }) {
                  return (
                    <div className="overflow-x-auto w-full">
                      <table className="border-collapse border border-gray-300 w-full" {...props} />
                    </div>
                  );
                },
                th({ node, ...props }) {
                  return <th className="border border-gray-300 px-4 py-2 bg-gray-100 dark:bg-gray-700" {...props} />;
                },
                td({ node, ...props }) {
                  return <td className="border border-gray-300 px-4 py-2" {...props} />;
                },
                // Ensure paragraphs wrap properly
                p({ node, ...props }) {
                  return <p className="whitespace-pre-wrap break-words" {...props} />;
                }
              }}
            >
              {replyText}
            </ReactMarkdown>
          )}
        </div>
        <div className="flex justify-end items-center gap-2">
          <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full" title="Like">
            <PiThumbsUp />
          </button>
          <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full" title="Dislike">
            <PiThumbsDown />
          </button>
          <button
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
            title="Copy response"
            onClick={copyToClipboard}
          >
            {copied ? <PiCheck /> : <PiCopy />}
          </button>
          <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full" title="Regenerate">
            <PiArrowsCounterClockwise />
          </button>
          <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full" title="Share">
            <PiShareFat />
          </button>
        </div>
      </div>
    </div>
  );
}

export default AiReply;
