@echo off
echo =======================================
echo    HearthForge Setup Script
echo =======================================
echo.

REM Check if Node.js is installed
echo Checking for Node.js...
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Node.js is not installed. Please install Node.js v20.10.0 or v22.12.0 or higher.
    echo Visit https://nodejs.org/ to download and install Node.js.
    exit /b 1
)

for /f "tokens=*" %%i in ('node -v') do set NODE_VERSION=%%i
echo Node.js is installed: %NODE_VERSION%

REM Check Node.js version
set NODE_VERSION_NUM=%NODE_VERSION:~1%
for /f "tokens=1,2 delims=." %%a in ("%NODE_VERSION_NUM%") do (
    set NODE_MAJOR=%%a
    set NODE_MINOR=%%b
)

if %NODE_MAJOR% LSS 20 (
    echo Node.js version is too old. Please install Node.js v20.10.0 or v22.12.0 or higher.
    echo Visit https://nodejs.org/ to download and install a newer version of Node.js.
    exit /b 1
) else if %NODE_MAJOR% EQU 20 (
    if %NODE_MINOR% LSS 10 (
        echo Node.js version is too old. Please install Node.js v20.10.0 or v22.12.0 or higher.
        echo Visit https://nodejs.org/ to download and install a newer version of Node.js.
        exit /b 1
    )
)

REM Check if npm is installed
echo Checking for npm...
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo npm is not installed. Please install npm v8.0.0 or v10.9.0 or higher.
    exit /b 1
)

for /f "tokens=*" %%i in ('npm -v') do set NPM_VERSION=%%i
echo npm is installed: %NPM_VERSION%

REM Check npm version
for /f "tokens=1 delims=." %%a in ("%NPM_VERSION%") do set NPM_MAJOR=%%a

if %NPM_MAJOR% LSS 8 (
    echo npm version is too old. Please install npm v8.0.0 or v10.9.0 or higher.
    exit /b 1
) else if %NPM_MAJOR% EQU 9 (
    echo Warning: npm v9.x.x is not explicitly supported. You might encounter issues.
    echo Recommended versions are npm v8.0.0+ or v10.9.0+.
)

REM Navigate to the MetaBloom directory
echo Navigating to the MetaBloom directory...
cd MetaBloom || (
    echo Failed to navigate to MetaBloom directory. Make sure you're running this script from the root of the repository.
    exit /b 1
)
echo Successfully navigated to MetaBloom directory.

REM Install dependencies
echo Installing dependencies...
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo Failed to install dependencies.
    exit /b 1
)
echo Successfully installed dependencies.

REM Create .env.local file if it doesn't exist
echo Checking for .env.local file...
if not exist .env.local (
    echo Creating .env.local file...
    (
        echo # Firebase Configuration
        echo # Replace these with your own Firebase project details
        echo NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
        echo NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
        echo NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project_id
        echo NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
        echo NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
        echo NEXT_PUBLIC_FIREBASE_APP_ID=your_firebase_app_id
        echo NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_firebase_measurement_id
    ) > .env.local
    echo Created .env.local file. Please update it with your Firebase configuration.
) else (
    echo .env.local file already exists.
)

REM Setup complete
echo.
echo =======================================
echo Setup complete! You can now start the development server with:
echo cd MetaBloom
echo npm run dev
echo.
echo The application will be available at:
echo http://localhost:3000
echo =======================================

pause
