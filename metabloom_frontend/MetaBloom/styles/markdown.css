/* Markdown content styling */
.markdown-content {
  line-height: 1.6;
  color: #333;
}

/* User message styling */
.user-response-container {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Headings */
.markdown-content h1 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  padding-bottom: 0.3rem;
  border-bottom: 1px solid #eaecef;
}

.markdown-content h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  padding-bottom: 0.3rem;
  border-bottom: 1px solid #eaecef;
}

.markdown-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-content h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-content h5, .markdown-content h6 {
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

/* Paragraphs and spacing */
.markdown-content p {
  margin-top: 0;
  margin-bottom: 1rem;
}

/* Lists */
.markdown-content ul, .markdown-content ol {
  margin-top: 0;
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content li {
  margin-bottom: 0.25rem;
}

.markdown-content li > ul, .markdown-content li > ol {
  margin-top: 0.25rem;
  margin-bottom: 0;
}

/* Blockquotes */
.markdown-content blockquote {
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  border-left: 4px solid #dfe2e5;
  color: #6a737d;
}

.markdown-content blockquote > :first-child {
  margin-top: 0;
}

.markdown-content blockquote > :last-child {
  margin-bottom: 0;
}

/* Code */
.markdown-content pre {
  margin-top: 0;
  margin-bottom: 1rem;
  padding: 1rem;
  overflow: auto;
  font-size: 0.85rem;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 3px;
}

.markdown-content code {
  font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
  font-size: 0.85rem;
  padding: 0.2em 0.4em;
  margin: 0;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  margin: 0;
  font-size: 100%;
  word-break: normal;
  white-space: pre;
  border: 0;
}

/* Links */
.markdown-content a {
  color: #0366d6;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

/* Images */
.markdown-content img {
  max-width: 100%;
  box-sizing: content-box;
  background-color: #fff;
}

/* Tables */
.markdown-content table {
  display: block;
  width: 100%;
  overflow: auto;
  margin-top: 0;
  margin-bottom: 1rem;
  border-spacing: 0;
  border-collapse: collapse;
}

.markdown-content table th {
  font-weight: 600;
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-content table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-content table tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}

.markdown-content table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

/* Horizontal Rule */
.markdown-content hr {
  height: 0.25em;
  padding: 0;
  margin: 24px 0;
  background-color: #e1e4e8;
  border: 0;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .markdown-content {
    color: #e4e4e4;
  }

  .markdown-content h1, .markdown-content h2 {
    border-bottom-color: #333;
  }

  .markdown-content blockquote {
    border-left-color: #444;
    color: #9e9e9e;
  }

  .markdown-content pre {
    background-color: #1e1e1e;
  }

  .markdown-content code {
    background-color: rgba(200, 200, 200, 0.1);
  }

  .markdown-content a {
    color: #58a6ff;
  }

  .markdown-content table th, .markdown-content table td {
    border-color: #444;
  }

  .markdown-content table tr {
    background-color: #1e1e1e;
    border-top-color: #444;
  }

  .markdown-content table tr:nth-child(2n) {
    background-color: #2a2a2a;
  }

  .markdown-content hr {
    background-color: #444;
  }
}

/* Additional styles for proper text wrapping */
.markdown-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  overflow-x: hidden;
}

.markdown-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  overflow-x: hidden;
}

.markdown-content code {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.markdown-content p, 
.markdown-content li, 
.markdown-content h1, 
.markdown-content h2, 
.markdown-content h3, 
.markdown-content h4, 
.markdown-content h5, 
.markdown-content h6 {
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  max-width: 100%;
}
