"use client";

import React from 'react';
import { useUserSubscription } from '@/hooks/useUserSubscription';
import { PiCrown, PiStar } from 'react-icons/pi';

function SubscriptionStatus() {
  const { hasActiveSubscription, getPlanDisplayName, currentPlan } = useUserSubscription();

  if (!hasActiveSubscription || currentPlan === 'free') {
    return null;
  }

  const planName = getPlanDisplayName();
  const icon = currentPlan === 'premium' ? PiCrown : PiStar;

  return (
    <div className="flex items-center gap-2 px-3 py-1 bg-primaryColor/10 border border-primaryColor/30 rounded-full">
      {React.createElement(icon, { className: "text-primaryColor text-sm" })}
      <span className="text-xs font-medium text-primaryColor">
        {planName}
      </span>
    </div>
  );
}

export default SubscriptionStatus;
