#!/bin/bash

# HearthForge Setup Script
# This script helps set up the HearthForge development environment

# Text colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=======================================${NC}"
echo -e "${BLUE}   HearthForge Setup Script   ${NC}"
echo -e "${BLUE}=======================================${NC}"
echo ""

# Check if Node.js is installed
echo -e "${YELLOW}Checking for Node.js...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js is not installed. Please install Node.js v20.10.0 or v22.12.0 or higher.${NC}"
    echo -e "${YELLOW}Visit https://nodejs.org/ to download and install Node.js.${NC}"
    exit 1
fi

NODE_VERSION=$(node -v)
echo -e "${GREEN}Node.js is installed: ${NODE_VERSION}${NC}"

# Check Node.js version
NODE_VERSION_NUM=$(echo $NODE_VERSION | sed 's/v//')
if [[ $(echo $NODE_VERSION_NUM | cut -d. -f1) -lt 20 ]]; then
    echo -e "${RED}Node.js version is too old. Please install Node.js v20.10.0 or v22.12.0 or higher.${NC}"
    echo -e "${YELLOW}Visit https://nodejs.org/ to download and install a newer version of Node.js.${NC}"
    exit 1
elif [[ $(echo $NODE_VERSION_NUM | cut -d. -f1) -eq 20 && $(echo $NODE_VERSION_NUM | cut -d. -f2) -lt 10 ]]; then
    echo -e "${RED}Node.js version is too old. Please install Node.js v20.10.0 or v22.12.0 or higher.${NC}"
    echo -e "${YELLOW}Visit https://nodejs.org/ to download and install a newer version of Node.js.${NC}"
    exit 1
fi

# Check if npm is installed
echo -e "${YELLOW}Checking for npm...${NC}"
if ! command -v npm &> /dev/null; then
    echo -e "${RED}npm is not installed. Please install npm v8.0.0 or v10.9.0 or higher.${NC}"
    exit 1
fi

NPM_VERSION=$(npm -v)
echo -e "${GREEN}npm is installed: ${NPM_VERSION}${NC}"

# Check npm version
NPM_VERSION_NUM=$NPM_VERSION
NPM_MAJOR=$(echo $NPM_VERSION_NUM | cut -d. -f1)
if [[ $NPM_MAJOR -lt 8 ]]; then
    echo -e "${RED}npm version is too old. Please install npm v8.0.0 or v10.9.0 or higher.${NC}"
    exit 1
elif [[ $NPM_MAJOR -eq 9 ]]; then
    echo -e "${YELLOW}Warning: npm v9.x.x is not explicitly supported. You might encounter issues.${NC}"
    echo -e "${YELLOW}Recommended versions are npm v8.0.0+ or v10.9.0+.${NC}"
fi

# Navigate to the MetaBloom directory
echo -e "${YELLOW}Navigating to the MetaBloom directory...${NC}"
cd MetaBloom || { echo -e "${RED}Failed to navigate to MetaBloom directory. Make sure you're running this script from the root of the repository.${NC}"; exit 1; }
echo -e "${GREEN}Successfully navigated to MetaBloom directory.${NC}"

# Install dependencies
echo -e "${YELLOW}Installing dependencies...${NC}"
npm install || { echo -e "${RED}Failed to install dependencies.${NC}"; exit 1; }
echo -e "${GREEN}Successfully installed dependencies.${NC}"

# Create .env.local file if it doesn't exist
echo -e "${YELLOW}Checking for .env.local file...${NC}"
if [ ! -f .env.local ]; then
    echo -e "${YELLOW}Creating .env.local file...${NC}"
    cat > .env.local << EOL
# Firebase Configuration
# Replace these with your own Firebase project details
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_firebase_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_firebase_measurement_id
EOL
    echo -e "${GREEN}Created .env.local file. Please update it with your Firebase configuration.${NC}"
else
    echo -e "${GREEN}.env.local file already exists.${NC}"
fi

# Setup complete
echo ""
echo -e "${BLUE}=======================================${NC}"
echo -e "${GREEN}Setup complete! You can now start the development server with:${NC}"
echo -e "${YELLOW}cd MetaBloom${NC}"
echo -e "${YELLOW}npm run dev${NC}"
echo ""
echo -e "${GREEN}The application will be available at:${NC}"
echo -e "${BLUE}http://localhost:3000${NC}"
echo -e "${BLUE}=======================================${NC}"
