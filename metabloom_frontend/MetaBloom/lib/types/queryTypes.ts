/**
 * Query Types for Two-Claude Architecture with Instruction Kit Support
 *
 * This module defines the interfaces for query specifications, data structures,
 * and communication between the Orchestrator (<PERSON> #1) and Processor (<PERSON> #2).
 * Updated to support the new instruction kit architecture.
 */

/**
 * Database types supported by the system
 */
export type DatabaseType = 'postgres' | 'graphql' | 'vector';

/**
 * Priority levels for queries
 */
export type QueryPriority = 'critical' | 'important' | 'supplementary';

/**
 * Response styles for the Processor
 */
export type ResponseStyle = 'analytical' | 'casual' | 'beginner_friendly' | 'expert';

/**
 * Expected response length
 */
export type ResponseLength = 'brief' | 'moderate' | 'comprehensive';

/**
 * Search intent types for the new architecture
 */
export type SearchIntent = 'find_cards' | 'find_decks' | 'find_archetypes' | 'find_synergies' | 'analyze_meta' | 'search_general';

/**
 * Instruction kit identifiers for getInstructionKit() function
 */
export type InstructionKitId =
  | 'postgres_table_names'
  | 'postgres_schema_details'
  | 'vector_search_methods'
  | 'graphql_schema'
  | 'query_examples'
  | 'error_handling_guide';

/**
 * Instruction kit content structure
 */
export interface InstructionKitContent {
  /** Kit identifier */
  kit_id: InstructionKitId;
  /** Human-readable description */
  description: string;
  /** Main content (JSON or text) */
  content: any;
  /** Version for cache invalidation */
  version: string;
  /** Last updated timestamp */
  last_updated: string;
  /** Usage examples if applicable */
  examples?: string[];
}

/**
 * Search specification interface - what to search for, not how
 */
export interface SearchSpecification {
  /** Unique identifier for the search */
  id: string;
  /** Type of search intent */
  intent: SearchIntent;
  /** Database to search against */
  database: DatabaseType;
  /** Search criteria and filters */
  criteria: {
    /** Terms to search for */
    searchTerms?: string[];
    /** Filters to apply */
    filters?: {
      className?: string;
      format?: string;
      manaCost?: number;
      rarity?: string;
      cardType?: string;
      deck_cards?: boolean;
      similarity_desc?: boolean;
      popularity_desc?: boolean;
      winrate_desc?: boolean;
      [key: string]: any; // Allow additional filters
    };
    /** Maximum number of results */
    limit?: number;
    /** Sort order specification */
    sortBy?: string;
    /** Additional context for the search */
    context?: Record<string, any>;
  };
  /** Purpose of the search for logging */
  purpose: string;
  /** Priority level */
  priority: QueryPriority;
}

/**
 * Collection of search specifications
 */
export interface SearchSpecifications {
  /** Primary searches that should be executed first */
  primary: SearchSpecification[];
  /** Conditional searches based on primary results */
  conditional: Array<{
    condition: string;
    specification: SearchSpecification;
  }>;
}

/**
 * Interface for an executable query (supports both legacy and specification formats)
 */
export interface ExecutableQuery {
  /** Unique identifier for the query */
  id: string;

  /** Database type to execute the query against */
  database: DatabaseType;

  /** The actual query string OR specification format */
  query: string;

  /** Parameters for the query */
  params: any[];

  /** Purpose of the query for context */
  purpose: string;

  /** Priority level of the query */
  priority: QueryPriority;
}

/**
 * Interface for a conditional query that may be executed based on certain conditions
 */
export interface ConditionalQuery {
  /** Condition that determines if this query should be executed */
  condition: string;

  /** Unique identifier for the query */
  id: string;

  /** Database type to execute the query against */
  database: DatabaseType;

  /** The actual query string OR specification format */
  query: string;

  /** Parameters for the query */
  params: any[];

  /** Purpose of the query for context */
  purpose: string;
}

/**
 * Interface for the set of executable queries (backward compatibility)
 */
export interface ExecutableQueries {
  /** Primary queries that should be executed first */
  primary: ExecutableQuery[];

  /** Conditional queries that may be executed based on results of primary queries */
  conditional: ConditionalQuery[];
}

/**
 * Deck statistics interface
 */
export interface DeckStats {
  /** Mana curve (count of cards by mana cost) */
  manaCurve: { [manaCost: number]: number };
  /** Card type distribution */
  cardTypes: {
    minions: number;
    spells: number;
    weapons: number;
    locations: number;
    heroes: number;
    other: number;
  };
  /** Rarity distribution */
  rarityDistribution: {
    common: number;
    rare: number;
    epic: number;
    legendary: number;
    free: number;
  };
  /** Keyword counts */
  keywords: { [keyword: string]: number };
  /** Average mana cost */
  averageManaCost: number;
  /** Estimated dust cost */
  estimatedDustCost: number;
}

/**
 * Processed deck data with validation information
 */
export interface ProcessedDeckData {
  /** The original deck code */
  deckCode: string;
  /** Whether the deck code is valid */
  isValid: boolean;
  /** Decoded deck information (if valid) */
  decoded?: {
    format: number;
    formatName: string;
    heroes: number[];
    className: string;
    cards: [number, number][]; // [cardId, count] pairs
    totalCards: number;
    uniqueCards: number;
    heroId: number;
  };
  /** Validation details */
  validation: {
    hasValidStructure: boolean;
    hasCards: boolean;
    hasValidFormat: boolean;
    hasValidHero: boolean;
  };
  /** Error message if invalid */
  error?: string;
  /** Deck statistics (if valid) */
  deckStats?: DeckStats;
}

/**
 * Interface for user context information
 */
export interface UserContext {
  /** The original message from the user */
  originalMessage: string;

  /** The detected intent of the user's message */
  intent: string;

  /** Confidence level in the intent classification (0-1) */
  confidence: number;

  /** Previous conversation history */
  conversationHistory: any[];
}

/**
 * Interface for processing instructions for the Processor
 */
export interface ProcessingInstructions {
  /** What to focus on in the response */
  primaryFocus: string;

  /** Style of the response */
  responseStyle: ResponseStyle;

  /** Expected length of the response */
  expectedLength: ResponseLength;

  /** Elements to include in the response */
  includeElements: string[];
}

/**
 * Enhanced API configuration for multiple databases
 */
export interface ApiConfig {
  /** Primary API endpoint URL */
  endpoint: string;

  /** Timeout in milliseconds */
  timeout: number;

  /** Number of retry attempts */
  retryAttempts: number;

  /** Database-specific endpoints (optional) */
  endpoints?: {
    postgres?: string;
    vector?: string;
    graphql?: string;
  };

  /** Authentication configuration (optional) */
  auth?: {
    apiKey?: string;
    bearerToken?: string;
    headers?: Record<string, string>;
  };
}

/**
 * Interface for the complete package sent from Orchestrator to Processor
 */
export interface OrchestratorPackage {
  /** User context information */
  userContext: UserContext;

  /** Decoded deck information (if applicable) */
  deckData: ProcessedDeckData | null;

  /** Executable queries for the Processor (backward compatibility) */
  executableQueries: ExecutableQueries;

  /** Search specifications for the new architecture */
  searchSpecifications?: SearchSpecifications;

  /** Instructions for the Processor */
  processingInstructions: ProcessingInstructions;

  /** API configuration */
  apiConfig: ApiConfig;
}

/**
 * Interface for data sufficiency analysis
 */
export interface DataSufficiencyCheck {
  /** Whether all critical data is available */
  hasCriticalData: boolean;

  /** Whether there is enough detail for a comprehensive response */
  hasEnoughDetail: boolean;

  /** Whether additional queries are needed */
  needsAdditionalQueries: boolean;

  /** Elements missing for a complete response */
  missingElements: string[];

  /** Confidence level in the current data set (0-1) */
  confidenceLevel: number;

  /** Recommended action based on data sufficiency */
  recommendedAction: 'synthesize' | 'query_more' | 'partial_response';
}

/**
 * Interface for query execution results
 */
export interface QueryResult {
  /** Whether the query was successful */
  success: boolean;

  /** The data returned by the query */
  data: any;

  /** Error message if the query failed */
  error?: string;

  /** Execution time in milliseconds */
  executionTime: number;

  /** Number of rows/items returned */
  rowCount?: number;

  /** Whether this failure is survivable (system can continue without this data) */
  survivableFailure?: boolean;

  /** Additional metadata */
  metadata?: {
    /** Data source used */
    source?: string;
    /** Cache hit/miss information */
    cached?: boolean;
    /** Query complexity score */
    complexity?: number;
  };
}

/**
 * Vector search parameters interface
 */
export interface VectorSearchParameters {
  /** Text to search for */
  text: string;
  /** Type of search */
  search_type: 'cards' | 'archetypes' | 'decks' | 'general';
  /** Search filters */
  filters?: Record<string, any>;
  /** Maximum number of results */
  limit?: number;
  /** Similarity threshold */
  threshold?: number;
}

/**
 * Search intent determination result
 */
export interface SearchIntentResult {
  /** Determined entity type */
  entityType: 'cards' | 'archetypes' | 'decks' | 'general';
  /** Primary search term */
  searchTerm: string;
  /** Fallback search terms */
  fallbackTerms: string[];
  /** Applied filters */
  filters: Record<string, any>;
  /** Result limit */
  limit?: number;
  /** Detected deck class */
  deckClass?: string;
  /** Detected format */
  format?: string;
}

/**
 * Database operation metadata
 */
export interface DatabaseOperationMetadata {
  /** Operation type */
  operation: 'query' | 'search' | 'analysis';
  /** Database used */
  database: DatabaseType;
  /** Instruction kit used (if any) */
  instructionKit?: InstructionKitId;
  /** Schema validation result */
  schemaValidated?: boolean;
  /** Performance metrics */
  performance?: {
    executionTime: number;
    resultCount: number;
    cacheHit: boolean;
  };
}

/**
 * Streaming response chunk interface
 */
export interface StreamingResponseChunk {
  /** Chunk type */
  type: 'content' | 'metadata' | 'error' | 'complete';
  /** Content data */
  content?: string;
  /** Accumulated response so far */
  fullResponse?: string;
  /** Token usage information */
  tokensUsed?: number;
  /** Error information (if type is 'error') */
  error?: string;
  /** Completion metadata (if type is 'complete') */
  metadata?: {
    totalTokens: number;
    processingTime: number;
    queriesExecuted: number;
  };
}

/**
 * Error handling types for better error categorization
 */
export type DatabaseErrorType =
  | 'connection_failed'
  | 'query_invalid'
  | 'schema_mismatch'
  | 'timeout'
  | 'rate_limited'
  | 'authentication_failed'
  | 'instruction_kit_missing';

/**
 * Structured error interface
 */
export interface DatabaseError {
  /** Error type for categorization */
  type: DatabaseErrorType;
  /** Human-readable error message */
  message: string;
  /** Technical details */
  details?: string;
  /** Database that failed */
  database?: DatabaseType;
  /** Query that caused the error */
  query?: string;
  /** Suggested recovery action */
  recovery?: string;
}

/**
 * Enhanced processor response metadata for the streaming architecture
 */
export interface ProcessorMetadata {
  /** Number of queries executed */
  queriesExecuted: number;

  /** Number of query rounds */
  queryRounds: number;

  /** Data sources used */
  dataSourcesUsed: DatabaseType[];

  /** Instruction kits consulted */
  instructionKitsUsed: InstructionKitId[];

  /** Processing time in milliseconds */
  processingTime: number;

  /** Confidence in response quality (0-1) */
  confidence: number;

  /** Completeness of the response (0-1) */
  completeness: number;

  /** Database operations performed */
  operations: DatabaseOperationMetadata[];
}

/**
 * Complete response envelope for non-streaming responses
 */
export interface ProcessorResponse {
  /** The complete, ready-to-deliver response */
  finalResponse: string;

  /** Metadata about the processing */
  processingMetadata: ProcessorMetadata;

  /** Any issues or limitations */
  limitations: {
    /** What data couldn't be retrieved */
    missingData: string[];

    /** Any assumptions made due to missing data */
    assumptionsMade: string[];

    /** Suggested follow-up questions */
    suggestedFollowUp: string[];
  };

  /** Success status */
  status: 'success' | 'partial_success' | 'error';

  /** Error message if status is error */
  error?: DatabaseError;
}