import { NextRequest } from 'next/server';
import { processWithOrchestrator } from '@/lib/claude/orchestrator';
import { processWithProcessor, processWithQueryPlanner } from '@/lib/claude/processor'; // Changed to non-streaming version
import { logSystemMessage, logError } from '@/lib/utils/claudeLogger';
import Anthropic from '@anthropic-ai/sdk';

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

// Configuration constants
const CONFIG = {
  MAX_RETRIES: 3,
  BASE_BACKOFF_MS: 1000,
  TOKEN_ESTIMATION_RATIO: 4, // ~4 chars per token (rough estimate)
  COOKIE_MAX_AGE: 60 * 60 * 24 * 365, // 1 year
  MAX_MESSAGE_LENGTH: 10000, // Prevent abuse
  USE_QUERY_PLANNER: true, // Toggle between legacy and new query planner
  DATABASE_API_CONFIG: {
    endpoint: 'https://wa6kt26wi1.execute-api.us-east-1.amazonaws.com/prod/query',
    timeout: 30000,
    retryAttempts: 3
  }
} as const;

// Using Node.js runtime to support file system operations for logging
// Note: This was changed from Edge runtime to support file-based logging

/**
 * Utility function for retry logic with exponential backoff
 */
async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = CONFIG.MAX_RETRIES,
  operationName: string = 'operation'
): Promise<T> {
  let retryCount = 0;

  while (retryCount < maxRetries) {
    try {
      return await operation();
    } catch (error: any) {
      // Check if this is an overloaded error that we should retry
      const isOverloaded =
        error.status === 529 ||
        (error.error && error.error.type === 'overloaded_error') ||
        (error.message && error.message.includes('Overloaded'));

      // If we've reached max retries or it's not an overloaded error, throw
      if (retryCount >= maxRetries - 1 || !isOverloaded) {
        logError('API', `Failed ${operationName} after ${retryCount + 1} attempts`, error);
        throw error;
      }

      // Calculate backoff time: 2^retryCount * base + random jitter
      const backoffTime = Math.pow(2, retryCount) * CONFIG.BASE_BACKOFF_MS + Math.random() * 1000;
      retryCount++;

      logSystemMessage(`${operationName} API overloaded. Retrying in ${Math.round(backoffTime / 1000)} seconds (attempt ${retryCount} of ${maxRetries})`);

      // Wait for the backoff time
      await new Promise(resolve => setTimeout(resolve, backoffTime));
    }
  }

  // This should never be reached, but TypeScript needs it
  throw new Error(`${operationName} failed after all retries`);
}

/**
 * Validate input message
 */
function validateMessage(message: string): void {
  if (!message || typeof message !== 'string') {
    throw new Error('Message is required and must be a string');
  }

  if (message.length > CONFIG.MAX_MESSAGE_LENGTH) {
    throw new Error(`Message too long (max ${CONFIG.MAX_MESSAGE_LENGTH} characters)`);
  }

  // Basic sanitization - remove null bytes and control characters
  if (/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/.test(message)) {
    throw new Error('Message contains invalid characters');
  }
}

/**
 * Estimate token usage for response
 */
function estimateTokens(text: string): number {
  return Math.ceil(text.length / CONFIG.TOKEN_ESTIMATION_RATIO);
}

/**
 * Create streaming response from orchestrator stream
 */
function createStreamingResponse(orchestratorStream: AsyncIterable<any>): Response {
  const encoder = new TextEncoder();

  const readableStream = new ReadableStream({
    async start(controller) {
      let fullResponse = '';
      let estimatedTokens = 0;

      try {
        for await (const chunk of orchestratorStream) {
          // Handle Claude's streaming format
          if (chunk.type === 'content_block_delta' && chunk.delta) {
            // Handle different delta formats
            const content = typeof chunk.delta === 'string'
              ? chunk.delta
              : 'text' in chunk.delta
                ? (chunk.delta.text || '')
                : '';

            if (content) {
              fullResponse += content;

              // Log streaming chunk with PII-safe preview
              logSystemMessage('Streaming Chunk', {
                content_preview: content.slice(0, 100), // First 100 chars only
                content_length: content.length,
                full_response_length: fullResponse.length,
                timestamp: new Date().toISOString(),
                chunk_number: Math.floor(fullResponse.length / content.length)
              });

              // Send the chunk to the client
              controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                content,
                fullResponse
              })}\n\n`));
            }
          }
        }

        // Calculate final token estimate
        estimatedTokens = estimateTokens(fullResponse);

        // Log final streaming completion
        logSystemMessage('Streaming Response Complete', {
          total_response_length: fullResponse.length,
          estimated_tokens: estimatedTokens,
          response_preview: fullResponse.slice(0, 200) + (fullResponse.length > 200 ? '...' : ''),
          timestamp: new Date().toISOString()
        });

        // Signal the end of the stream with token info
        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
          content: '',
          fullResponse,
          tokensUsed: estimatedTokens
        })}\n\n`));
        controller.enqueue(encoder.encode('data: [DONE]\n\n'));

      } catch (error) {
        logError('API', 'Error in streaming response', error);
        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
          error: 'Stream processing failed'
        })}\n\n`));
      } finally {
        controller.close();
      }
    },
  });

  // Create response with Set-Cookie header for token tracking
  const response = new Response(readableStream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });

  return response;
}

/**
 * Generate final user response from structured processor data
 */
async function generateFinalResponse(processorData: any, message: string, chatHistory: any[]): Promise<AsyncIterable<any>> {
  try {
    logSystemMessage('Orchestrator generating final user response from structured data');

    // Extract information from the structured processor response
    const structuredResponse = processorData?.structuredResponse || {};
    const decks = structuredResponse.decks || [];
    const cards = structuredResponse.cards || [];
    const synergies = structuredResponse.synergies || [];
    const summary = structuredResponse.summary || '';
    const confidence = structuredResponse.confidence || 0.5;
    const dataQuality = structuredResponse.dataQuality || 'unknown';

    // Extract deck names and additional metadata
    const deckNames = decks.map((deck: any) => deck.name).filter(Boolean);
    const deckDetails = decks.map((deck: any) => ({
      name: deck.name,
      class: deck.class,
      winrate: deck.winrate,
      notes: deck.notes
    })).filter((deck: any) => deck.name);

    // Extract card information
    const cardNames = cards.map((card: any) => card.name).filter(Boolean);
    const cardDetails = cards.map((card: any) => ({
      name: card.name,
      mana_cost: card.mana_cost,
      type: card.type,
      text: card.text,
      class: card.class
    })).filter((card: any) => card.name);

    // Extract synergy information
    const synergyDetails = synergies.map((synergy: any) => ({
      card1: synergy.card1,
      card2: synergy.card2,
      description: synergy.description
    })).filter((synergy: any) => synergy.card1 && synergy.card2);

    logSystemMessage('Extracted Structured Data for Final Response (Structured)', {
      deck_names_count: deckNames.length,
      deck_names_preview: deckNames.slice(0, 3), // First 3 deck names only
      card_names_count: cardNames.length,
      card_names_preview: cardNames.slice(0, 3), // First 3 card names only
      synergies_count: synergyDetails.length,
      summary_length: summary.length,
      summary_preview: summary.slice(0, 100) + (summary.length > 100 ? '...' : ''),
      confidence: confidence,
      data_quality: dataQuality,
      timestamp: new Date().toISOString()
    });

    // Build the final prompt with structured data
    let dataText = '';

    // Add deck information
    if (deckNames.length > 0) {
      const deckListText = deckDetails.map((deck: any, i: number) => {
        let line = `${i + 1}. ${deck.name}`;
        if (deck.winrate) line += ` (${deck.winrate} winrate)`;
        if (deck.notes) line += ` - ${deck.notes}`;
        return line;
      }).join('\n');
      dataText += `Current meta decks:\n${deckListText}\n\n`;
    }

    // Add card information
    if (cardNames.length > 0) {
      const cardListText = cardDetails.map((card: any, i: number) => {
        let line = `${i + 1}. ${card.name}`;
        if (card.mana_cost !== undefined) line += ` (${card.mana_cost} mana)`;
        if (card.type) line += ` - ${card.type}`;
        if (card.class) line += ` (${card.class})`;
        return line;
      }).join('\n');
      dataText += `Relevant cards:\n${cardListText}\n\n`;
    }

    // Add synergy information
    if (synergyDetails.length > 0) {
      const synergyListText = synergyDetails.map((synergy: any, i: number) => {
        return `${i + 1}. ${synergy.card1} synergizes with ${synergy.card2}`;
      }).join('\n');
      dataText += `Card synergies:\n${synergyListText}\n\n`;
    }

    // Enhanced logic to handle partial data intelligently
    const hasUsableData = deckNames.length > 0 || cardNames.length > 0 || synergyDetails.length > 0;

    if (!dataText && !hasUsableData) {
      dataText = 'No current data available from the database.\n\n';
    } else if (!dataText && hasUsableData) {
      // We have structured data but no dataText was built (shouldn't happen, but safety check)
      dataText = 'Some data was retrieved from the database.\n\n';
    }

    // Enhanced prompt that encourages using partial data
    const FINAL_ORCHESTRATOR_PROMPT = `You are a Hearthstone assistant responding to users.

The user asked: "${message}"

Here is the data found in the database:
${dataText}${summary ? `Database summary: ${summary}\n\n` : ''}${hasUsableData ? 'IMPORTANT: Use the data provided above to give specific, helpful advice. Even if some queries failed, provide meaningful guidance based on the available information.\n\n' : ''}${confidence < 0.7 ? 'Note: The data confidence is moderate, so provide appropriate caveats.\n\n' : ''}Provide a helpful response using the EXACT information provided above. ${hasUsableData ? 'Focus on the specific decks, cards, and synergies listed.' : 'If no relevant data is available, explain that current information is not available and suggest general guidance.'} Do not make up deck names, card names, or other information not present in the data.`;

    // PII-Safe logging of Final Response API call
    const redactedFinalPrompt = FINAL_ORCHESTRATOR_PROMPT
      .replace(/[A-Za-z0-9+/=]{20,150}/g, '[DECK_CODE_REDACTED]') // Redact deck codes
      .replace(/AAE[A-Za-z0-9+/=]+/g, '[HEARTHSTONE_DECK_CODE]') // Redact Hearthstone deck codes
      .replace(/"[^"]{50,}"/g, '"[LONG_USER_INPUT_REDACTED]"') // Redact long quoted strings
      .slice(0, 800); // Truncate to 800 chars for final prompt

    const finalSystemPrompt = 'You are a helpful Hearthstone assistant. Use only the deck names and information provided to you. Do not hallucinate or make up deck names.';

    logSystemMessage('Final Response API Call (PII-Safe)', {
      system_prompt: finalSystemPrompt,
      user_prompt_length: FINAL_ORCHESTRATOR_PROMPT.length,
      user_prompt_redacted: redactedFinalPrompt,
      model: 'claude-3-5-sonnet-20241022',
      temperature: 0.7,
      max_tokens: 4096,
      stream: true,
      deck_count: deckNames.length,
      card_count: cardNames.length,
      synergy_count: synergyDetails.length,
      confidence: confidence,
      data_quality: dataQuality
    });

    const stream = await anthropic.messages.create({
      model: 'claude-3-5-sonnet-20241022',
      system: finalSystemPrompt,
      messages: [{ role: 'user', content: FINAL_ORCHESTRATOR_PROMPT }],
      max_tokens: 4096,
      temperature: 0.7,
      stream: true,
    });

    return stream;
  } catch (error) {
    logError('API', 'Error generating final response', error);
    throw error;
  }
}

/**
 * Handle API errors with appropriate status codes and messages
 */
function handleApiError(error: any, context: string): Response {
  // Check if this is a client disconnect
  if (error.code === 'ECONNRESET' || error.message?.includes('aborted')) {
    logSystemMessage(`Client disconnected during ${context}: ${error.message || error.code}`);
    return new Response(JSON.stringify({ error: 'Client closed request' }), {
      status: 499, // Client Closed Request
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // Handle specific error types
  let statusCode = 500;
  let errorMessage = 'Internal server error';

  if (error.message?.includes('Message too long')) {
    statusCode = 413; // Payload Too Large
    errorMessage = error.message;
  } else if (error.message?.includes('invalid characters')) {
    statusCode = 400; // Bad Request
    errorMessage = 'Invalid message format';
  } else if (error.status) {
    statusCode = error.status;
    errorMessage = error.message || 'API error';
  }

  logError('API', `${context} error`, error);

  return new Response(JSON.stringify({
    error: errorMessage,
    message: statusCode >= 500 ? 'Please try again later' : error.message
  }), {
    status: statusCode,
    headers: { 'Content-Type': 'application/json' },
  });
}

export async function POST(req: NextRequest) {
  try {
    const { message, chatHistory } = await req.json();

    // Validate input
    validateMessage(message);

    logSystemMessage(`Received chat request: ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}`);

    // Process the message through the Two-Claude Architecture
    logSystemMessage('Processing message through Two-Claude Architecture');

    let processorResult;
    if (CONFIG.USE_QUERY_PLANNER) {
      // Use new dynamic query planner (Claude 2 only)
      logSystemMessage('Step 1: Processing with Query Planner (Claude 2 only)');
      processorResult = await withRetry(
        () => processWithQueryPlanner(message, chatHistory, CONFIG.DATABASE_API_CONFIG),
        CONFIG.MAX_RETRIES,
        'Query Planner'
      );
    } else {
      // Use legacy orchestrator-based approach (Claude 1 → Claude 2)
      logSystemMessage('Step 1: Processing with Orchestrator');
      const orchestratorResult = await withRetry(
        () => processWithOrchestrator(message, chatHistory),
        CONFIG.MAX_RETRIES,
        'Orchestrator'
      );

      logSystemMessage('Step 2: Processor executes queries and synthesizes data (Legacy mode)');
      processorResult = await withRetry(
        () => processWithProcessor(orchestratorResult.data, message, chatHistory), // Non-streaming version
        CONFIG.MAX_RETRIES,
        'Processor'
      );
    }

    // Final Step: Generate and stream user response
    logSystemMessage(`Final Step: Generate and stream user response (${CONFIG.USE_QUERY_PLANNER ? 'Query Planner' : 'Legacy'} mode)`);
    const finalResponseStream = await withRetry(
      () => generateFinalResponse(processorResult, message, chatHistory),
      CONFIG.MAX_RETRIES,
      'Final Response'
    );

    // Stream the final response to user
    const response = createStreamingResponse(finalResponseStream);

    // Add token tracking via response headers
    response.headers.set('X-Estimated-Tokens', '0');

    return response;

  } catch (error: any) {
    // Handle different types of errors appropriately
    return handleApiError(error, 'POST request processing');
  }
}
