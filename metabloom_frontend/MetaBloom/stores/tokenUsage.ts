import { create } from "zustand";
import { persist } from "zustand/middleware";

type TokenUsageState = {
  tokensUsed: number;
  incrementTokens: (amount: number) => void;
  resetTokens: () => void;
};

export const useTokenUsage = create<TokenUsageState>()(
  persist(
    (set) => ({
      tokensUsed: 0,
      incrementTokens: (amount: number) => 
        set((state) => ({ tokensUsed: state.tokensUsed + amount })),
      resetTokens: () => set({ tokensUsed: 0 }),
    }),
    {
      name: "token-usage-storage",
    }
  )
);
