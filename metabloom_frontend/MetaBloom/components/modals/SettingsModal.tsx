import SmallButtons from "@/components/ui/buttons/SmallButtons";
import Link from "next/link";
import React from "react";
import { PiCreditCard, PiRocket, PiX } from "react-icons/pi";

function SettingsModal() {
  return (
    <div className="dark:text-n30">
      <div className="mt-6 bg-primaryColor/5 border border-primaryColor/30 rounded-xl p-5">
        <div className="pb-5 border-b border-primaryColor/30">
          <p className="text-n700 font-medium dark:text-n30">Subscription Management</p>
          <p className="pt-2 text-xs">
            Manage your subscription and payment details
          </p>
        </div>
        <div className="flex flex-col gap-4 pt-5">
          {/* Upgrade Plan */}
          <div className="p-4 rounded-xl hover:bg-primaryColor/5 hover:border-primaryColor/30 border border-transparent duration-500">
            <Link
              href="/upgrade-plan"
              className="flex justify-between items-center w-full"
            >
              <div>
                <p className="text-n700 font-medium dark:text-n30 text-sm flex items-center gap-2">
                  <PiRocket className="text-xl text-primaryColor" />
                  Upgrade Plan
                </p>
                <p className="pt-2 text-xs">
                  View available plans and upgrade your subscription
                </p>
              </div>
              <div className="text-primaryColor text-sm font-medium">
                View Plans
              </div>
            </Link>
          </div>

          {/* Update Payment Info */}
          <div className="p-4 rounded-xl hover:bg-primaryColor/5 hover:border-primaryColor/30 border border-transparent duration-500">
            <button className="flex justify-between items-center w-full">
              <div>
                <p className="text-n700 font-medium dark:text-n30 text-sm flex items-center gap-2">
                  <PiCreditCard className="text-xl text-primaryColor" />
                  Update Payment Information
                </p>
                <p className="pt-2 text-xs">
                  Change or update your payment method
                </p>
              </div>
              <div className="text-primaryColor text-sm font-medium">
                Update
              </div>
            </button>
          </div>

          {/* Cancel Subscription */}
          <div className="p-4 rounded-xl hover:bg-errorColor/5 hover:border-errorColor/30 border border-transparent duration-500">
            <button className="flex justify-between items-center w-full">
              <div>
                <p className="text-n700 font-medium dark:text-n30 text-sm flex items-center gap-2">
                  <PiX className="text-xl text-errorColor" />
                  Cancel Subscription
                </p>
                <p className="pt-2 text-xs">
                  Cancel your current subscription plan
                </p>
              </div>
              <div className="text-errorColor text-sm font-medium">
                Cancel
              </div>
            </button>
          </div>
        </div>

        <div className="flex justify-start items-center gap-2 pt-5 text-xs">
          <SmallButtons name="Save Changes" />
        </div>
      </div>
    </div>
  );
}

export default SettingsModal;
