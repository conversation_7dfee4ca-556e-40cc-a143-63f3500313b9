import { demoChatData } from "@/constants/chats";
import { create } from "zustand";

type ChatMessagesType = {
  id?: string;
  text:
    | string
    | {
        summary: string;
        isCommandSuggestion?: boolean;
        commands?: { command: string; label: string }[];
        code?: string;
        language?: string;
        image?: string;
      };
  isUser: boolean;
  timestamp: string;
  isStreaming?: boolean;
};

export interface Chat {
  id: string;
  title: string;
  messages: ChatMessagesType[];
}

type ChatHandlerType = {
  userQuery: string;
  chatList: Chat[];
  isAnimation: boolean;
  handleSubmit: (text: string, chatId: string) => void;
  updateChatList: () => void;
  emptyQuery: () => void;
  setUserQuery: (text: string) => void;
  addStreamingMessage: (chatId: string, message: ChatMessagesType) => void;
  updateStreamingMessage: (chatId: string, messageId: string, text: string) => void;
  finalizeStreamingMessage: (chatId: string, messageId: string, finalText: string) => void;
  addErrorMessage: (chatId: string, errorMessage: ChatMessagesType) => void;
};

export const useChatHandler = create<ChatHandlerType>((set) => ({
  userQuery: "",
  chatList: [], // Initialize with empty array instead of demo data
  isAnimation: false,
  setUserQuery: (text) => {
    set({ userQuery: text });
  },
  emptyQuery: () => {
    set({ userQuery: "" });
  },
  updateChatList: () => {
    try {
      // Only load chat history if there's data in localStorage
      const storedData = localStorage.getItem("chat-list");
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        set({ chatList: parsedData });
      }
    } catch (error) {
      console.log(error);
    }
  },
  handleSubmit: async (text, chatId) => {
    set({ userQuery: text });

    const timestamp = new Date().toISOString();
    const userMessage = { text, isUser: true, timestamp };

    // Check if user is authenticated by checking if there's data in localStorage
    const isAuthenticated = localStorage.getItem("auth-storage") !== null;

    // Only save chat history for authenticated users
    if (isAuthenticated) {
      // Load chat list from localStorage
      const storedData = localStorage.getItem("chat-list");
      let chatList: Chat[] = storedData ? JSON.parse(storedData) : [];

      // Add or update the user's message in the correct chat
      const chatExists = chatList.some((chat) => chat.id === chatId);
      if (!chatExists) {
        chatList = [
          {
            id: chatId,
            title: text,
            messages: [userMessage],
          },
          ...chatList,
        ];
      } else {
        chatList = chatList.map((chat) => {
          if (chat.id === chatId) {
            return {
              ...chat,
              messages: [...chat.messages, userMessage],
            };
          }
          return chat;
        });
      }

      // Save the chat list with the user's message
      localStorage.setItem("chat-list", JSON.stringify(chatList));
      set({ chatList });
    } else {
      // For non-authenticated users, still update the state for the current session
      // but don't save to localStorage
      set((state) => {
        const chatExists = state.chatList.some((chat) => chat.id === chatId);
        let updatedChatList = [...state.chatList];

        if (!chatExists) {
          updatedChatList = [
            {
              id: chatId,
              title: text,
              messages: [userMessage],
            },
            ...updatedChatList,
          ];
        } else {
          updatedChatList = updatedChatList.map((chat) => {
            if (chat.id === chatId) {
              return {
                ...chat,
                messages: [...chat.messages, userMessage],
              };
            }
            return chat;
          });
        }

        return {
          chatList: updatedChatList,
          userQuery: ""
        };
      });
    }
  },

  // Add a streaming message placeholder
  addStreamingMessage: (chatId, message) => {
    set((state) => {
      // Check if chat exists
      const existingChatIndex = state.chatList.findIndex(chat => chat.id === chatId);
      let updatedChatList = [...state.chatList];

      if (existingChatIndex === -1) {
        // Create a new chat if it doesn't exist
        const newChat = {
          id: chatId,
          title: typeof message.text === 'string' ? message.text : 'New Chat',
          messages: [message]
        };

        updatedChatList = [newChat, ...updatedChatList];
      } else {
        // Update existing chat
        updatedChatList[existingChatIndex] = {
          ...updatedChatList[existingChatIndex],
          messages: [...updatedChatList[existingChatIndex].messages, message]
        };
      }

      // Save to localStorage if authenticated
      const isAuthenticated = localStorage.getItem("auth-storage") !== null;
      if (isAuthenticated) {
        try {
          localStorage.setItem("chat-list", JSON.stringify(updatedChatList));
        } catch (error) {
          console.error("Error saving to localStorage:", error);
        }
      }

      return { chatList: updatedChatList };
    });
  },

  // Update a streaming message as new chunks arrive
  updateStreamingMessage: (chatId, messageId, text) => {
    set((state) => {
      const existingChatIndex = state.chatList.findIndex(chat => chat.id === chatId);

      if (existingChatIndex === -1) {
        return state;
      }

      const chat = state.chatList[existingChatIndex];
      const messageIndex = chat.messages.findIndex(msg => msg.id === messageId);

      if (messageIndex === -1) {
        return state;
      }

      // Create updated message
      const updatedMessage = {
        ...chat.messages[messageIndex],
        text: text,
      };

      // Create updated messages array
      const updatedMessages = [...chat.messages];
      updatedMessages[messageIndex] = updatedMessage;

      // Create updated chat
      const updatedChat = {
        ...chat,
        messages: updatedMessages
      };

      // Also update the temporary navigation state if it exists
      try {
        const navKey = `nav-chat-${chatId}`;
        const navChatJson = sessionStorage.getItem(navKey);
        if (navChatJson) {
          const navChat = JSON.parse(navChatJson);
          const navMessageIndex = navChat.messages.findIndex(msg => msg.id === messageId);

          if (navMessageIndex !== -1) {
            navChat.messages[navMessageIndex] = updatedMessage;
            sessionStorage.setItem(navKey, JSON.stringify(navChat));
          }
        }
      } catch (error) {
        console.error("Error updating navigation chat state:", error);
      }

      // Create updated chat list
      const updatedChatList = [...state.chatList];
      updatedChatList[existingChatIndex] = updatedChat;

      return { chatList: updatedChatList };
    });
  },

  // Finalize a streaming message when complete
  finalizeStreamingMessage: (chatId, messageId, finalText) => {
    set((state) => {
      const existingChatIndex = state.chatList.findIndex(chat => chat.id === chatId);

      if (existingChatIndex === -1) {
        return state;
      }

      const chat = state.chatList[existingChatIndex];
      const messageIndex = chat.messages.findIndex(msg => msg.id === messageId);

      if (messageIndex === -1) {
        return state;
      }

      // Create finalized message
      const finalizedMessage = {
        ...chat.messages[messageIndex],
        text: finalText,
        isStreaming: false
      };

      // Create updated messages array
      const updatedMessages = [...chat.messages];
      updatedMessages[messageIndex] = finalizedMessage;

      // Create updated chat
      const updatedChat = {
        ...chat,
        messages: updatedMessages
      };

      // Also update the temporary navigation state if it exists
      try {
        const navKey = `nav-chat-${chatId}`;
        const navChatJson = sessionStorage.getItem(navKey);
        if (navChatJson) {
          const navChat = JSON.parse(navChatJson);
          const navMessageIndex = navChat.messages.findIndex(msg => msg.id === messageId);

          if (navMessageIndex !== -1) {
            navChat.messages[navMessageIndex] = finalizedMessage;
            sessionStorage.setItem(navKey, JSON.stringify(navChat));
          }
        }
      } catch (error) {
        console.error("Error updating navigation chat state:", error);
      }

      // Create updated chat list
      const updatedChatList = [...state.chatList];
      updatedChatList[existingChatIndex] = updatedChat;

      // Save to localStorage if authenticated
      const isAuthenticated = localStorage.getItem("auth-storage") !== null;
      if (isAuthenticated) {
        try {
          localStorage.setItem("chat-list", JSON.stringify(updatedChatList));
        } catch (error) {
          console.error("Error saving to localStorage:", error);
        }
      }

      return {
        chatList: updatedChatList,
        isAnimation: false,
        userQuery: ""
      };
    });
  },

  // Add an error message
  addErrorMessage: (chatId, errorMessage) => {
    set((state) => {
      const existingChatIndex = state.chatList.findIndex(chat => chat.id === chatId);

      if (existingChatIndex === -1) {
        // Create a new chat if it doesn't exist
        return {
          chatList: [
            {
              id: chatId,
              title: "Error",
              messages: [errorMessage]
            },
            ...state.chatList
          ],
          isAnimation: false
        };
      }

      // Update existing chat
      const updatedChatList = [...state.chatList];
      updatedChatList[existingChatIndex] = {
        ...updatedChatList[existingChatIndex],
        messages: [...updatedChatList[existingChatIndex].messages, errorMessage]
      };

      return {
        chatList: updatedChatList,
        isAnimation: false
      };
    });
  },

}));
