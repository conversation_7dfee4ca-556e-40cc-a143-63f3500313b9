[2025-05-25T18:53:43.199Z] [SYSTEM] Received chat request: hi
-------------------------------------------
[2025-05-25T18:53:43.200Z] [SYSTEM] Processing message through Two-<PERSON> Architecture
-------------------------------------------
[2025-05-25T18:53:43.200Z] [SYSTEM] Step 1: Processing with Query Planner (<PERSON> 2 only)
-------------------------------------------
[2025-05-25T18:53:43.201Z] [PROCESSOR] Starting query planner workflow
{
  "userMessage": "hi",
  "chatHistoryLength": 1
}
-------------------------------------------
[2025-05-25T18:53:43.204Z] [ERROR:Processor] Failed to call <PERSON> after 1 attempts
{
  "details": {
    "error": {},
    "messageConfig": {
      "model": "claude-3-5-sonnet-20241022",
      "system_prompt_length": 1600,
      "messages_count": 3,
      "max_tokens": 4096,
      "temperature": 0.3,
      "stream": false
    },
    "retry_attempts": 1,
    "max_retries": 5,
    "error_type": "other",
    "timestamp": "2025-05-25T18:53:43.204Z"
  }
}
-------------------------------------------
[2025-05-25T18:53:43.206Z] [ERROR:Processor] Error in query planner workflow
{
  "message": "Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the \"X-Api-Key\" or \"Authorization\" headers to be explicitly omitted",
  "stack": "Error: Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the \"X-Api-Key\" or \"Authorization\" headers to be explicitly omitted\n    at Anthropic.validateHeaders (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:9767:15)\n    at Anthropic.buildHeaders (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:10147:14)\n    at Anthropic.buildRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:10094:33)\n    at Anthropic.makeRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:9882:44)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callAnthropicWithRetry (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:2973:30)\n    at async processWithQueryPlanner (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:3099:26)\n    at async withRetry (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:3658:20)\n    at async POST (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:3951:31)\n    at async AppRouteRouteModule.do (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:10:32847)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:10:39868)\n    at async doRender (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:1452:42)\n    at async responseGenerator (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:1822:28)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:1832:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:2259:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:2297:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:959:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/next-server.js:281:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:853:17)\n    at async /Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/trace/trace.js:153:20)\n    at async DevServer.handleRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/router-server.js:230:21)\n    at async handleRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/router-server.js:408:24)\n    at async requestHandlerImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/router-server.js:432:13)\n    at async Server.requestListener (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/start-server.js:146:13)",
  "details": {}
}
-------------------------------------------
[2025-05-25T18:53:43.207Z] [ERROR:API] Failed Query Planner after 1 attempts
{
  "message": "Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the \"X-Api-Key\" or \"Authorization\" headers to be explicitly omitted",
  "stack": "Error: Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the \"X-Api-Key\" or \"Authorization\" headers to be explicitly omitted\n    at Anthropic.validateHeaders (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:9767:15)\n    at Anthropic.buildHeaders (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:10147:14)\n    at Anthropic.buildRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:10094:33)\n    at Anthropic.makeRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:9882:44)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callAnthropicWithRetry (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:2973:30)\n    at async processWithQueryPlanner (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:3099:26)\n    at async withRetry (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:3658:20)\n    at async POST (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:3951:31)\n    at async AppRouteRouteModule.do (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:10:32847)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:10:39868)\n    at async doRender (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:1452:42)\n    at async responseGenerator (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:1822:28)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:1832:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:2259:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:2297:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:959:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/next-server.js:281:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:853:17)\n    at async /Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/trace/trace.js:153:20)\n    at async DevServer.handleRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/router-server.js:230:21)\n    at async handleRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/router-server.js:408:24)\n    at async requestHandlerImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/router-server.js:432:13)\n    at async Server.requestListener (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/start-server.js:146:13)",
  "details": {}
}
-------------------------------------------
[2025-05-25T18:53:43.207Z] [ERROR:API] POST request processing error
{
  "message": "Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the \"X-Api-Key\" or \"Authorization\" headers to be explicitly omitted",
  "stack": "Error: Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the \"X-Api-Key\" or \"Authorization\" headers to be explicitly omitted\n    at Anthropic.validateHeaders (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:9767:15)\n    at Anthropic.buildHeaders (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:10147:14)\n    at Anthropic.buildRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:10094:33)\n    at Anthropic.makeRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/node_modules_128e73._.js:9882:44)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callAnthropicWithRetry (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:2973:30)\n    at async processWithQueryPlanner (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:3099:26)\n    at async withRetry (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:3658:20)\n    at async POST (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/.next/server/chunks/[root of the server]__5162fa._.js:3951:31)\n    at async AppRouteRouteModule.do (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:10:32847)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:10:39868)\n    at async doRender (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:1452:42)\n    at async responseGenerator (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:1822:28)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:1832:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:2259:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:2297:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:959:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/next-server.js:281:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/base-server.js:853:17)\n    at async /Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/trace/trace.js:153:20)\n    at async DevServer.handleRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/router-server.js:230:21)\n    at async handleRequest (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/router-server.js:408:24)\n    at async requestHandlerImpl (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/router-server.js:432:13)\n    at async Server.requestListener (/Users/<USER>/Desktop/Dcg_Hearth/metabloom_frontend/MetaBloom/node_modules/next/dist/server/lib/start-server.js:146:13)",
  "details": {}
}
-------------------------------------------
[2025-05-25T18:58:23.346Z] [SYSTEM] Received chat request: hi
-------------------------------------------
[2025-05-25T18:58:23.347Z] [SYSTEM] Processing message through Two-Claude Architecture
-------------------------------------------
[2025-05-25T18:58:23.347Z] [SYSTEM] Step 1: Processing with Query Planner (Claude 2 only)
-------------------------------------------
[2025-05-25T18:58:23.349Z] [PROCESSOR] Starting query planner workflow
{
  "userMessage": "hi",
  "chatHistoryLength": 1
}
-------------------------------------------
[2025-05-25T18:58:27.200Z] [PROCESSOR] Received query plan from Claude 2
{
  "rawPlan": "{\n  \"queries\": [],\n  \"response_expectation\": {\n    \"assemble_summary\": false\n  }\n}"
}
-------------------------------------------
[2025-05-25T18:58:27.201Z] [PROCESSOR] Executing dynamic query plan
{
  "queryCount": 0,
  "databases": [],
  "queryTypes": []
}
-------------------------------------------
[2025-05-25T18:58:27.202Z] [PROCESSOR] Query plan execution complete
{
  "totalQueries": 0,
  "successfulQueries": 0,
  "failedQueries": 0,
  "survivableFailures": 0,
  "warnings": 0,
  "partialFailures": 0,
  "canContinue": false
}
-------------------------------------------
[2025-05-25T18:58:27.203Z] [PROCESSOR] Deck extraction filter for _metadata
{
  "hasData": false,
  "dataType": "undefined",
  "dataLength": "N/A"
}
-------------------------------------------
[2025-05-25T18:58:27.203Z] [PROCESSOR] Synergy extraction filter for _metadata
{
  "hasData": false,
  "dataType": "undefined"
}
-------------------------------------------
[2025-05-25T18:58:27.204Z] [PROCESSOR] Structured Data Generated
{
  "decks": {
    "count": 0,
    "preview": []
  },
  "cards": {
    "count": 0,
    "preview": []
  },
  "synergies": {
    "count": 0,
    "preview": []
  },
  "confidence": 0.1,
  "dataQuality": "low",
  "summary": "No data could be retrieved",
  "warnings": 0,
  "partialFailures": 0
}
-------------------------------------------
[2025-05-25T18:58:27.204Z] [PROCESSOR] Query Result Detail: _metadata
{
  "error": null,
  "dataType": "undefined",
  "dataPreview": null
}
-------------------------------------------
[2025-05-25T18:58:27.205Z] [PROCESSOR] Query planner workflow complete
{
  "queriesExecuted": 1,
  "successfulQueries": 0,
  "failedQueries": 1,
  "structuredDataKeys": [
    "rawResults",
    "warnings",
    "partialFailures",
    "confidence",
    "dataQuality",
    "summary"
  ],
  "warnings": 0,
  "partialFailures": 0,
  "canContinue": false,
  "hasUsableData": false
}
-------------------------------------------
[2025-05-25T18:58:27.205Z] [SYSTEM] Final Step: Generate and stream user response (Query Planner mode)
-------------------------------------------
[2025-05-25T18:58:27.205Z] [SYSTEM] Orchestrator generating final user response from structured data
-------------------------------------------
[2025-05-25T18:58:27.206Z] [SYSTEM] Extracted Structured Data for Final Response (Structured)
{
  "deck_names_count": 0,
  "deck_names_preview": [],
  "card_names_count": 0,
  "card_names_preview": [],
  "synergies_count": 0,
  "summary_length": 26,
  "summary_preview": "No data could be retrieved",
  "confidence": 0.1,
  "data_quality": "low",
  "timestamp": "2025-05-25T18:58:27.206Z"
}
-------------------------------------------
[2025-05-25T18:58:27.206Z] [SYSTEM] Final Response API Call (PII-Safe)
{
  "system_prompt": "You are a helpful Hearthstone assistant. Use only the deck names and information provided to you. Do not hallucinate or make up deck names.",
  "user_prompt_length": 549,
  "user_prompt_redacted": "You are a Hearthstone assistant responding to users.\n\nThe user asked: \"hi\"\n\nHere is the data found in the database:\nNo current data available from the database.\n\nDatabase summary: No data could be retrieved\n\nNote: The data confidence is moderate, so provide appropriate caveats.\n\nProvide a helpful response using the EXACT information provided above. If no relevant data is available, explain that current information is not available and suggest general guidance. Do not make up deck names, card names, or other information not present in the data.",
  "model": "claude-3-5-sonnet-20241022",
  "temperature": 0.7,
  "max_tokens": 4096,
  "stream": true,
  "deck_count": 0,
  "card_count": 0,
  "synergy_count": 0,
  "confidence": 0.1,
  "data_quality": "low"
}
-------------------------------------------
[2025-05-25T18:58:28.745Z] [SYSTEM] Streaming Chunk
{
  "content_preview": "Hi",
  "content_length": 2,
  "full_response_length": 2,
  "timestamp": "2025-05-25T18:58:28.745Z",
  "chunk_number": 1
}
-------------------------------------------
[2025-05-25T18:58:28.902Z] [SYSTEM] Streaming Chunk
{
  "content_preview": " there! I'm a Hearth",
  "content_length": 20,
  "full_response_length": 22,
  "timestamp": "2025-05-25T18:58:28.902Z",
  "chunk_number": 1
}
-------------------------------------------
[2025-05-25T18:58:29.020Z] [SYSTEM] Streaming Chunk
{
  "content_preview": "stone assistant here to help you",
  "content_length": 32,
  "full_response_length": 54,
  "timestamp": "2025-05-25T18:58:29.020Z",
  "chunk_number": 1
}
-------------------------------------------
[2025-05-25T18:58:29.132Z] [SYSTEM] Streaming Chunk
{
  "content_preview": ". Currently, I don't have",
  "content_length": 25,
  "full_response_length": 79,
  "timestamp": "2025-05-25T18:58:29.132Z",
  "chunk_number": 3
}
-------------------------------------------
[2025-05-25T18:58:29.227Z] [SYSTEM] Streaming Chunk
{
  "content_preview": " access to any specific deck or",
  "content_length": 31,
  "full_response_length": 110,
  "timestamp": "2025-05-25T18:58:29.226Z",
  "chunk_number": 3
}
-------------------------------------------
[2025-05-25T18:58:29.538Z] [SYSTEM] Streaming Chunk
{
  "content_preview": " card data in my database. However, I",
  "content_length": 37,
  "full_response_length": 147,
  "timestamp": "2025-05-25T18:58:29.538Z",
  "chunk_number": 3
}
-------------------------------------------
[2025-05-25T18:58:29.657Z] [SYSTEM] Streaming Chunk
{
  "content_preview": "'m happy to try to help answer",
  "content_length": 30,
  "full_response_length": 177,
  "timestamp": "2025-05-25T18:58:29.657Z",
  "chunk_number": 5
}
-------------------------------------------
[2025-05-25T18:58:29.801Z] [SYSTEM] Streaming Chunk
{
  "content_preview": " general Hearthstone questions or",
  "content_length": 33,
  "full_response_length": 210,
  "timestamp": "2025-05-25T18:58:29.801Z",
  "chunk_number": 6
}
-------------------------------------------
[2025-05-25T18:58:29.991Z] [SYSTEM] Streaming Chunk
{
  "content_preview": " provide basic guidance about the game. What",
  "content_length": 44,
  "full_response_length": 254,
  "timestamp": "2025-05-25T18:58:29.991Z",
  "chunk_number": 5
}
-------------------------------------------
[2025-05-25T18:58:30.054Z] [SYSTEM] Streaming Chunk
{
  "content_preview": " would you like to know",
  "content_length": 23,
  "full_response_length": 277,
  "timestamp": "2025-05-25T18:58:30.054Z",
  "chunk_number": 12
}
-------------------------------------------
[2025-05-25T18:58:30.168Z] [SYSTEM] Streaming Chunk
{
  "content_preview": "?",
  "content_length": 1,
  "full_response_length": 278,
  "timestamp": "2025-05-25T18:58:30.168Z",
  "chunk_number": 278
}
-------------------------------------------
[2025-05-25T18:58:30.217Z] [SYSTEM] Streaming Response Complete
{
  "total_response_length": 278,
  "estimated_tokens": 70,
  "response_preview": "Hi there! I'm a Hearthstone assistant here to help you. Currently, I don't have access to any specific deck or card data in my database. However, I'm happy to try to help answer general Hearthstone qu...",
  "timestamp": "2025-05-25T18:58:30.217Z"
}
-------------------------------------------
