"use client";
import React from "react";
import { PiStar, PiArrowClockwise } from "react-icons/pi";
import { useTokenUsage } from "@/stores/tokenUsage";

function TokenCounter() {
  const { tokensUsed, resetTokens } = useTokenUsage();

  const handleReset = () => {
    resetTokens();
  };

  return (
    <div className="flex items-center gap-2">
      <div className="relative flex flex-col items-center">
        {/* Token count badge positioned directly above the star */}
        <div className="bg-primaryColor text-white text-xs font-medium rounded-full px-2 py-0.5 mb-1 text-center">
          {tokensUsed.toLocaleString()}
        </div>

        {/* Star icon */}
        <div className="text-[28px] text-warningColor">
          <PiStar />
        </div>
      </div>

      {/* Label aligned with star */}
      <div className="font-medium text-sm mt-7">
        Tokens
      </div>

      {/* Reset button */}
      <button
        onClick={handleReset}
        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200 mt-7 ml-1"
        title="Reset token counter"
      >
        <PiArrowClockwise className="text-sm" />
      </button>
    </div>
  );
}

export default TokenCounter;
