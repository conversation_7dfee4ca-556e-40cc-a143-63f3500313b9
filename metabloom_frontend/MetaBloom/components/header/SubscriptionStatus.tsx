"use client";

import React from 'react';
import { useSubscription } from '@/stores/subscription';
import { PiCrown } from 'react-icons/pi';

function SubscriptionStatus() {
  const { hasActiveSubscription, subscriptionPlan } = useSubscription();

  if (!hasActiveSubscription) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 px-3 py-1 bg-primaryColor/10 border border-primaryColor/30 rounded-full">
      <PiCrown className="text-primaryColor text-sm" />
      <span className="text-xs font-medium text-primaryColor">
        {subscriptionPlan || 'Pro'}
      </span>
    </div>
  );
}

export default SubscriptionStatus;
