.typing-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #888;
  margin: 0 3px;
  animation: typing 1.4s infinite ease-in-out both;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Disabled button and input styles */
button:disabled,
input:disabled,
textarea:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Textarea styles */
textarea {
  line-height: 1.5;
  font-family: inherit;
}

/* Error color for the stop button */
.bg-errorColor {
  background-color: #ef4444;
}

.border-errorColor {
  border-color: #ef4444;
}
