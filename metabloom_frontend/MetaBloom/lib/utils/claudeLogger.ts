/**
 * <PERSON> Lo<PERSON>
 *
 * This module provides logging functionality for the Two-Claude Architecture.
 * It logs the communication between the Orchestrator and Processor to help with debugging.
 */

import fs from 'fs';
import path from 'path';

// Define log file paths
const LOG_DIR = path.join(process.cwd(), 'logs');
const ORCHESTRATOR_LOG = path.join(LOG_DIR, 'orchestrator.log');
const PROCESSOR_LOG = path.join(LOG_DIR, 'processor.log');
const COMMUNICATION_LOG = path.join(LOG_DIR, 'claude_communication.log');

// Ensure log directory exists
try {
  if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
  }
} catch (error) {
  console.error('Error creating log directory:', error);
}

/**
 * Log a message to a specific log file
 *
 * @param logFile The path to the log file
 * @param message The message to log
 * @param data Optional data to include in the log
 */
function logToFile(logFile: string, message: string, data?: any) {
  try {
    const timestamp = new Date().toISOString();
    let logEntry = `[${timestamp}] ${message}\n`;

    if (data) {
      // Format data as pretty JSON if it's an object
      if (typeof data === 'object') {
        logEntry += JSON.stringify(data, null, 2) + '\n';
      } else {
        logEntry += String(data) + '\n';
      }
    }

    logEntry += '-------------------------------------------\n';

    // Append to log file
    fs.appendFileSync(logFile, logEntry);
  } catch (error) {
    console.error(`Error writing to log file ${logFile}:`, error);
  }
}

/**
 * Log an Orchestrator message
 *
 * @param message The message to log
 * @param data Optional data to include in the log
 */
export function logOrchestrator(message: string, data?: any) {
  logToFile(ORCHESTRATOR_LOG, message, data);
  logToFile(COMMUNICATION_LOG, `[ORCHESTRATOR] ${message}`, data);
}

/**
 * Log a Processor message
 *
 * @param message The message to log
 * @param data Optional data to include in the log
 */
export function logProcessor(message: string, data?: any) {
  logToFile(PROCESSOR_LOG, message, data);
  logToFile(COMMUNICATION_LOG, `[PROCESSOR] ${message}`, data);
}

/**
 * Log a function call
 *
 * @param functionName The name of the function being called
 * @param params The parameters passed to the function
 * @param result The result of the function call
 */
export function logFunctionCall(functionName: string, params: any[], result: any) {
  const message = `Function Call: ${functionName}`;
  const data = {
    params,
    result
  };

  logToFile(COMMUNICATION_LOG, message, data);
}

/**
 * Log an error
 *
 * @param source The source of the error (e.g., 'Orchestrator', 'Processor')
 * @param message The error message
 * @param error The error object
 */
export function logError(source: string, message: string, error: any) {
  const errorData = {
    message: error.message,
    stack: error.stack,
    details: error
  };

  logToFile(COMMUNICATION_LOG, `[ERROR:${source}] ${message}`, errorData);
}

/**
 * Log a user message
 *
 * @param message The user's message
 */
export function logUserMessage(message: string) {
  logToFile(COMMUNICATION_LOG, `[USER] Message`, message);
}

/**
 * Log a system message
 *
 * @param message The system message
 * @param data Optional data to include in the log
 */
export function logSystemMessage(message: string, data?: any) {
  logToFile(COMMUNICATION_LOG, `[SYSTEM] ${message}`, data);
}

/**
 * Log a database operation
 *
 * @param database The database type (postgres, vector, neo4j, graphql)
 * @param operation The operation being performed
 * @param details Details about the operation
 * @param result The result of the operation
 */
export function logDatabaseOperation(database: string, operation: string, details: any, result?: any) {
  const message = `[DATABASE:${database}] ${operation}`;
  const data = {
    details,
    ...(result && { result })
  };

  logToFile(PROCESSOR_LOG, message, data);
  logToFile(COMMUNICATION_LOG, message, data);
}
