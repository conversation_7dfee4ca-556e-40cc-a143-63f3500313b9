"use client";
import React, { FormEvent, useState } from "react";
import FormInput from "@/components/ui/FormInput";
import Link from "next/link";
import { useMainModal } from "@/stores/modal";
import { useAuth } from "@/contexts/AuthContext";
import GoogleSignInButton from "@/components/ui/GoogleSignInButton";

interface AuthModalProps {
  mode: "signin" | "signup";
}

function AuthModal({ mode }: AuthModalProps) {
  const isSignIn = mode === "signin";
  const [userEmail, setUserEmail] = useState("");
  const [userPassword, setUserPassword] = useState("");
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const { modalClose, modalOpen } = useMainModal();

  const { login, signup, loginWithGoogle } = useAuth();

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsError(false);

    try {
      if (isSignIn) {
        // Sign in logic using Firebase
        await login(userEmail, userPassword);
        // Close modal and continue with the chat
        modalClose();
      } else {
        // Sign up logic using Firebase
        if (userEmail && userPassword.length >= 6) {
          await signup(userEmail, userPassword);
          // Close modal and continue with the chat
          modalClose();
        } else {
          setIsError(true);
        }
      }
    } catch (error: any) {
      console.error("Authentication error:", error);

      // Set appropriate error message based on Firebase error code
      setIsError(true);
      if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
        setErrorMessage("Invalid email or password. Please try again.");
      } else if (error.code === 'auth/email-already-in-use') {
        setErrorMessage("This email is already registered. Please sign in instead.");
      } else if (error.code === 'auth/weak-password') {
        setErrorMessage("Password should be at least 6 characters.");
      } else if (error.code === 'auth/invalid-email') {
        setErrorMessage("Please enter a valid email address.");
      } else if (error.code === 'auth/network-request-failed') {
        setErrorMessage("Network error. Please check your internet connection.");
      } else {
        setErrorMessage("An error occurred. Please try again later.");
      }
    }
  };

  return (
    <div className="w-full">
      <div className="w-full pt-4">
        <p className="text-2xl font-semibold">
          {isSignIn ? "Welcome Back!" : "Create an Account"}
        </p>
        <p className="text-sm pt-4">
          {isSignIn
            ? "Sign in to your account and join us"
            : "Create an account to start chatting with MetaBloom"}
        </p>

        <form
          onSubmit={handleSubmit}
          className="pt-6 grid grid-cols-2 gap-4 sm:gap-6"
        >
          <div className="col-span-2">
            <FormInput
              title="Enter Your Email ID"
              placeholder="Your email ID here"
              type="email"
              value={userEmail}
              onChange={(e) => setUserEmail(e.target.value)}
            />
          </div>
          <div className="col-span-2">
            <FormInput
              title="Password"
              placeholder="*******"
              type="password"
              value={userPassword}
              onChange={(e) => setUserPassword(e.target.value)}
            />
            {isSignIn && (
              <Link
                href={"/"}
                className="text-end block pt-4 text-primaryColor text-sm"
              >
                Forget password?
              </Link>
            )}
          </div>

          {isSignIn && (
            <p className="col-span-2 text-sm pt-2">
              Don't have an account?{" "}
              <button
                type="button"
                onClick={() => modalOpen("Authentication", { mode: "signup" })}
                className="text-primaryColor font-semibold"
              >
                Sign Up
              </button>
            </p>
          )}
          {!isSignIn && (
            <p className="col-span-2 text-sm pt-2">
              Already have an account?{" "}
              <button
                type="button"
                onClick={() => modalOpen("Authentication", { mode: "signin" })}
                className="text-primaryColor font-semibold"
              >
                Sign In
              </button>
            </p>
          )}

          <div className="col-span-2">
            <button
              type="submit"
              className="text-sm font-medium text-white bg-primaryColor text-center py-3 px-6 rounded-full block w-full"
            >
              {isSignIn ? "Sign In" : "Create Account"}
            </button>
            {isError && (
              <p className="text-errorColor text-sm pt-2">
                {errorMessage || (isSignIn
                  ? "Please enter correct information"
                  : "Please enter a valid email and password (min 6 characters)")}
              </p>
            )}
          </div>
        </form>

        <div className="pt-8">
          <div className="flex justify-center items-center">
            <div className="bg-n30 flex-1 h-px"></div>
            <p className="text-xs px-2">Or Continue With</p>
            <div className="bg-n30 flex-1 h-px"></div>
          </div>
          <div className="pt-4 text-center">
            <div className="flex justify-center">
              <GoogleSignInButton
                rounded={true}
                shadow={true}
                hoverEffect={true}
                label={isSignIn ? "Sign in with Google" : "Sign up with Google"}
                onClick={async () => {
                  try {
                    setIsError(false);
                    setErrorMessage("");
                    await loginWithGoogle();
                    modalClose();
                  } catch (error: any) {
                    console.error("Google login error:", error);
                    setIsError(true);
                    if (error.code === 'auth/popup-closed-by-user') {
                      setErrorMessage("Sign-in popup was closed. Please try again.");
                    } else if (error.code === 'auth/popup-blocked') {
                      setErrorMessage("Sign-in popup was blocked. Please allow popups for this site.");
                    } else if (error.code === 'auth/cancelled-popup-request') {
                      // This is normal when multiple popups are attempted, no need to show error
                      setIsError(false);
                    } else if (error.code === 'auth/network-request-failed') {
                      setErrorMessage("Network error. Please check your internet connection.");
                    } else {
                      setErrorMessage("An error occurred with Google sign-in. Please try again.");
                    }
                  }
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthModal;
