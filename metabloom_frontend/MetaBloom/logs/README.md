# Claude Communication Logs

This directory contains logs of the communication between the Orchestrator and Processor components of the Two-Claude Architecture.

## Log Files

- `claude_communication.log`: Combined log of all communication between components
- `orchestrator.log`: Log of the Orchestrator component's activities
- `processor.log`: Log of the Processor component's activities

## Viewing Logs

You can view the logs using the provided script:

```bash
# View the combined communication log
node scripts/view-logs.js

# View the orchestrator log
node scripts/view-logs.js orchestrator

# View the processor log
node scripts/view-logs.js processor
```

## Log Format

Each log entry includes:
- Timestamp
- Source component (ORCHESTRATOR, PROCESSOR, USER, SYSTEM)
- Message
- Optional data (formatted as JSON)
- Separator line

Example:
```
[2023-06-01T12:34:56.789Z] [ORCHESTRATOR] Processing user message
{
  "message": "What are some good Mage cards?"
}
-------------------------------------------
```

## Purpose

These logs are intended for debugging and development purposes. They provide visibility into:

1. How user messages are processed by the Orchestrator
2. What database functions are called and their results
3. How the Processor generates responses based on the Orchestrator's output
4. Any errors that occur during processing

This helps identify issues in the Two-Claude Architecture and understand how the components interact.
