import { useChat<PERSON><PERSON><PERSON> } from "@/stores/chatList";
import { usePathname, useRouter } from "next/navigation";
import React, { FormEvent, useState, useRef, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";
import {
  PiArrowUp,
  PiLightbulb,
  PiMagnifyingGlass,
  PiPaperclip,
  PiSquare,
} from "react-icons/pi";
import { useAuth } from "@/stores/auth";
import { useMainModal } from "@/stores/modal";
import { useTokenUsage } from "@/stores/tokenUsage";

function ChatBox() {
  const [inputText, setInputText] = useState("");
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamedResponse, setStreamedResponse] = useState("");
  const streamedResponseRef = useRef("");
  const abortControllerRef = useRef<AbortController | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const router = useRouter();
  const path = usePathname();
  const {
    userQuery,
    setUserQuery,
    addStreamingMessage,
    updateStreamingMessage,
    finalizeStreamingMessage,
    addErrorMessage,
    chatList
  } = useChatHandler();
  const { isAuthenticated } = useAuth();
  const { modalOpen } = useMainModal();
  const { incrementTokens } = useTokenUsage();

  // Flag to control visibility of search and brainstorm bubbles - set to false to hide them
  const showSearchBrainstormBubbles = false;

  const chatIdUrl = path.split("/chat/")[1];

  // Function to stop streaming
  const handleStopStreaming = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();

      // If we have a partial response, finalize it
      if (streamedResponseRef.current && streamedResponseRef.current.trim().length > 0) {
        // Get the current chat ID
        const currentChatId = path.includes("/chat/") ? chatIdUrl : "";

        // Find the most recent AI message ID
        const chat = chatList.find(chat => chat.id === currentChatId);
        if (chat) {
          const aiMessages = chat.messages.filter(msg => !msg.isUser && msg.isStreaming);
          if (aiMessages.length > 0) {
            const lastAiMessage = aiMessages[aiMessages.length - 1];
            if (lastAiMessage.id) {
              // Finalize the message with what we have so far
              finalizeStreamingMessage(currentChatId, lastAiMessage.id, streamedResponseRef.current + "\n\n[Generation stopped by user]");
            }
          }
        }
      }

      setIsStreaming(false);
      abortControllerRef.current = null;
    }
  };

  // Track if we're in the middle of navigation
  const isNavigatingRef = useRef(false);

  // Function to auto-resize the textarea
  const autoResizeTextarea = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto';
      // Set the height to the scrollHeight
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  };

  // Auto-resize textarea when input changes
  useEffect(() => {
    autoResizeTextarea();
  }, [inputText]);

  // Clean up on unmount, but don't abort during navigation
  useEffect(() => {
    return () => {
      // Only abort if we're not navigating between pages
      if (abortControllerRef.current && !isNavigatingRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const handleSendMessage = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (userQuery.length === 0) {
      return;
    }

    // Check if user is authenticated
    if (!isAuthenticated) {
      // If not authenticated, show the authentication modal
      modalOpen("Authentication");
      return;
    }

    // If we're authenticated, we should continue with the message
    // No need for additional checks here

    // Cancel any ongoing stream
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create a new abort controller
    abortControllerRef.current = new AbortController();

    // Get current chat ID
    let currentChatId = "";
    if (!path.includes("/chat/")) {
      // For new chats, generate a new ID but don't navigate yet
      currentChatId = uuidv4();
    } else {
      // For existing chats, use the ID from the URL
      currentChatId = chatIdUrl;
    }

    // Add user message to state
    const userMessage = {
      text: userQuery,
      isUser: true,
      timestamp: new Date().toISOString()
    };

    // Add user message to chat
    addStreamingMessage(currentChatId, userMessage);
    setInputText("");
    setIsStreaming(true);
    setStreamedResponse("");
    streamedResponseRef.current = "";

    try {
      // Create a placeholder message for the AI response
      const aiMessageId = uuidv4();
      const aiMessage = {
        id: aiMessageId,
        text: '',
        isUser: false,
        timestamp: new Date().toISOString(),
        isStreaming: true
      };

      // Get chat history for context
      const existingChat = chatList.find(chat => chat.id === currentChatId);

      // If this is a brand new chat with no history, add a system note to help the AI
      // understand this is a new conversation
      let chatHistory = [];

      if (existingChat && existingChat.messages.length > 0) {
        // Get last 10 messages for context in existing chats
        chatHistory = existingChat.messages.map(msg => ({
          role: msg.isUser ? 'user' : 'assistant',
          content: typeof msg.text === 'string' ? msg.text : ''
        })).slice(-10);
      } else {
        // For brand new chats, add a system note
        chatHistory = [{
          role: 'system',
          content: 'This is the first message in a new conversation.'
        }];
      }

      // Call your API with streaming
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          message: userQuery,
          chatHistory: chatHistory
        }),
        signal: abortControllerRef.current.signal
      });

      // Only add the AI message placeholder after we've confirmed the API call is working
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      // Add the placeholder to the chat
      addStreamingMessage(currentChatId, aiMessage);

      // If we're not already on a chat page, navigate to the new chat
      if (!path.includes("/chat/")) {
        // Set the navigation flag to prevent aborting during navigation
        isNavigatingRef.current = true;

        // Store the current chat state in sessionStorage to ensure it's available after navigation
        try {
          // Create a temporary storage key for this specific navigation
          const navKey = `nav-chat-${currentChatId}`;

          // Find the current chat in the chatList
          const currentChat = chatList.find(chat => chat.id === currentChatId);

          if (currentChat) {
            // Store the current chat state
            sessionStorage.setItem(navKey, JSON.stringify(currentChat));
          }
        } catch (error) {
          console.error("Error storing chat state for navigation:", error);
        }

        // Use router.push and wait for it to complete
        router.push(`/chat/${currentChatId}`);
      }

      // If we're navigating, add a small delay to ensure navigation completes
      if (isNavigatingRef.current) {
        // Small delay to allow navigation to complete
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Process the stream
      const reader = response.body?.getReader();
      if (!reader) throw new Error("Response body is null");

      // Process the stream chunks
      const decoder = new TextDecoder();
      let done = false;

      while (!done) {
        const { value, done: doneReading } = await reader.read();
        done = doneReading;

        if (done) break;

        // Decode and process the chunk
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.substring(6);

            if (data === '[DONE]') {
              // Stream is complete
              break;
            }

            try {
              const { content, fullResponse, tokensUsed } = JSON.parse(data);

              // Check if this is a function call from Claude
              if (fullResponse && fullResponse.startsWith('__FUNCTION_CALL__:')) {
                // Extract the function call data
                const functionCallData = JSON.parse(fullResponse.replace('__FUNCTION_CALL__:', ''));
                const { functionName, params } = functionCallData;

                console.log(`Claude is calling function: ${functionName} with params:`, params);

                // Make a new API call to execute the function
                try {
                  const functionResponse = await fetch("/api/chat", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                      message: fullResponse, // Send the function call as the message
                      chatHistory: [] // No need for chat history for function calls
                    })
                  });

                  if (!functionResponse.ok) {
                    throw new Error(`Function API error: ${functionResponse.status}`);
                  }

                  const functionResult = await functionResponse.json();
                  console.log(`Function ${functionName} result:`, functionResult);

                  // For now, we'll just continue with the streaming response
                  // In a more advanced implementation, we might want to send the function result back to Claude
                } catch (functionError) {
                  console.error(`Error executing function ${functionName}:`, functionError);
                }

                // Don't update the UI with the function call
                return;
              }

              // Update the streamed response
              if (fullResponse) {
                // If we get a full response from the server, use that
                streamedResponseRef.current = fullResponse;
                setStreamedResponse(fullResponse);
              } else if (content) {
                // Otherwise append the new content to what we have
                streamedResponseRef.current += content;
                setStreamedResponse(streamedResponseRef.current);
              }

              // If we received token usage information, update the token counter
              if (tokensUsed) {
                incrementTokens(tokensUsed);
              }

              // Update the streaming message in the chat with the current full response
              updateStreamingMessage(currentChatId, aiMessageId, streamedResponseRef.current);
            } catch (e) {
              console.error('Error parsing streaming data:', e);
            }
          }
        }
      }

      // Get the final response from ref and finalize the message
      const finalResponse = streamedResponseRef.current;
      if (finalResponse && finalResponse.trim().length > 0) {
        finalizeStreamingMessage(currentChatId, aiMessageId, finalResponse);
      } else {
        // If somehow we don't have a response, add a fallback message
        finalizeStreamingMessage(currentChatId, aiMessageId, "I'm sorry, I couldn't generate a proper response. Please try again.");
      }

    } catch (error: any) {
      // Check if this is an abort error during navigation (which we can ignore)
      const isNavigationAbort = error.name === 'AbortError' && isNavigatingRef.current;

      if (error.name === 'AbortError' && !isNavigatingRef.current) {
        console.log('Stream was aborted by user');
      } else if (!isNavigationAbort) {
        console.error("Error calling AI:", error);
        // Handle error - add an error message to the chat
        const errorMessage = {
          text: "Sorry, I encountered an error processing your request. Please try again.",
          isUser: false,
          timestamp: new Date().toISOString()
        };

        // Add error message to chat
        addErrorMessage(currentChatId, errorMessage);
      }
    } finally {
      setIsStreaming(false);
      abortControllerRef.current = null;
      isNavigatingRef.current = false; // Reset navigation flag
    }
  };
  return (
    <div className=" w-full max-w-[1070px] mx-auto px-4 sm:px-6">
      <form
        onSubmit={handleSendMessage}
        className={`  w-full bg-primaryColor/5 p-2 lg:p-4 rounded-xl border border-primaryColor/20  `}
      >
        <div className="w-full bg-white rounded-lg max-lg:text-sm block dark:bg-n0">
          <textarea
            ref={textareaRef}
            className="w-full outline-none p-4 pb-3 bg-transparent resize-none min-h-[56px] max-h-[200px] overflow-y-auto"
            placeholder={inputText ? inputText : "Ask @HearthForge to generate you a god tier deck"}
            value={inputText}
            onChange={(e) => {
              setUserQuery(e.target.value);
              setInputText(e.target.value);
            }}
            onKeyDown={(e) => {
              // Submit on Enter (without Shift)
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (inputText.trim().length > 0 && !isStreaming) {
                  handleSendMessage(e as unknown as FormEvent<HTMLFormElement>);
                }
              }
            }}
            rows={1}
            disabled={isStreaming}
          />
        </div>

        <div className="flex justify-between items-center pt-4">
          <div className="flex justify-start items-center gap-3">
            {/* Search and Brainstorm bubbles - hidden but code preserved for future use */}
            {showSearchBrainstormBubbles && (
              <>
                <button className="flex justify-center items-center gap-2 py-2 px-2 sm:px-4 rounded-full border border-secondaryColor/20 bg-secondaryColor/5">
                  <PiMagnifyingGlass className="text-secondaryColor" />
                  <span className="text-xs font-medium max-sm:hidden">Search</span>
                </button>
                <button className="flex justify-center items-center gap-2 py-2 px-2 sm:px-4 rounded-full border border-warningColor/30 bg-warningColor/5">
                  <PiLightbulb className="text-warningColor" />
                  <span className="text-xs font-medium max-sm:hidden">
                    Brainstorm
                  </span>
                </button>
              </>
            )}
            {/* Upload file button moved to the left side */}
            <button className="bg-white p-2 rounded-full flex justify-center items-center border border-primaryColor/20 dark:bg-n0">
              <PiPaperclip />
            </button>
          </div>
          <div className="flex justify-end items-center gap-3">
            {/* Send/Stop button that changes based on streaming state */}
            {isStreaming ? (
              <button
                type="button"
                onClick={handleStopStreaming}
                className="bg-errorColor p-2 rounded-full flex justify-center items-center border border-errorColor text-white"
                aria-label="Stop generating"
                title="Stop generating"
              >
                <PiSquare />
              </button>
            ) : (
              <button
                type="submit"
                className="bg-primaryColor p-2 rounded-full flex justify-center items-center border border-primaryColor text-white"
                aria-label="Send message"
                title="Send message"
                disabled={userQuery.trim().length === 0}
              >
                <PiArrowUp />
              </button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}

export default ChatBox;
