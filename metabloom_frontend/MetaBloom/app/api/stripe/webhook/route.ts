import { NextRequest, NextResponse } from 'next/server';
import { getStripe } from '@/lib/stripe';
import { updateUserSubscription, getUserProfile, createUserProfile } from '@/lib/subscription';
import Stripe from 'stripe';

// This should be set in your environment variables
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
  const body = await request.text();
  const signature = request.headers.get('stripe-signature')!;

  let event: Stripe.Event;

  try {
    const stripe = getStripe();
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return NextResponse.json(
      { error: 'Webhook signature verification failed' },
      { status: 400 }
    );
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session;
        await handleCheckoutSessionCompleted(session);
        break;

      case 'customer.subscription.created':
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionCreated(subscription);
        break;

      case 'customer.subscription.updated':
        const updatedSubscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionUpdated(updatedSubscription);
        break;

      case 'customer.subscription.deleted':
        const deletedSubscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionDeleted(deletedSubscription);
        break;

      case 'invoice.payment_succeeded':
        const invoice = event.data.object as Stripe.Invoice;
        await handleInvoicePaymentSucceeded(invoice);
        break;

      case 'invoice.payment_failed':
        const failedInvoice = event.data.object as Stripe.Invoice;
        await handleInvoicePaymentFailed(failedInvoice);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  console.log('Checkout session completed:', session.id);

  const { userId, planType, isYearly } = session.metadata || {};

  if (!userId || !planType) {
    console.error('Missing userId or planType in session metadata');
    return;
  }

  try {
    // Get the subscription from Stripe
    const stripe = getStripe();
    const subscription = await stripe.subscriptions.list({
      customer: session.customer as string,
      status: 'active',
      limit: 1,
    });

    const activeSubscription = subscription.data[0];

    if (activeSubscription) {
      // Update user subscription in Firestore
      await updateUserSubscription(userId, {
        stripeCustomerId: session.customer as string,
        stripeSubscriptionId: activeSubscription.id,
        planType: planType as 'standard' | 'premium',
        status: 'active',
        currentPeriodStart: activeSubscription.current_period_start * 1000,
        currentPeriodEnd: activeSubscription.current_period_end * 1000,
        cancelAtPeriodEnd: false,
        isYearly: isYearly === 'true',
        updatedAt: Date.now(),
      });

      console.log(`User ${userId} successfully subscribed to ${planType} plan (yearly: ${isYearly})`);
    }
  } catch (error) {
    console.error('Error handling checkout session completed:', error);
  }
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  console.log('Subscription created:', subscription.id);
  // Handle new subscription creation
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log('Subscription updated:', subscription.id);

  try {
    // Find user by Stripe subscription ID
    const stripe = getStripe();
    const customer = await stripe.customers.retrieve(subscription.customer as string);

    if ('email' in customer && customer.email) {
      // You might need to find user by email or store customer ID mapping
      // For now, we'll update based on subscription ID if we can find the user
      console.log(`Subscription ${subscription.id} updated for customer ${customer.email}`);

      // Update subscription status in database
      // This would require finding the user by stripeSubscriptionId
    }
  } catch (error) {
    console.error('Error handling subscription update:', error);
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log('Subscription deleted:', subscription.id);

  try {
    // Find user by Stripe subscription ID and update their status
    // This would require finding the user by stripeSubscriptionId
    console.log(`Subscription ${subscription.id} canceled`);

    // Update user to free plan
    // await updateUserSubscription(userId, {
    //   planType: 'free',
    //   status: 'canceled',
    //   updatedAt: Date.now(),
    // });
  } catch (error) {
    console.error('Error handling subscription deletion:', error);
  }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log('Invoice payment succeeded:', invoice.id);
  // Handle successful payment
  // Extend subscription, send receipt, etc.
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  console.log('Invoice payment failed:', invoice.id);
  // Handle failed payment
  // Send notification, retry payment, etc.
}
