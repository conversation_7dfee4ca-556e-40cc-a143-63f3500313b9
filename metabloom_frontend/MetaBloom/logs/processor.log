[2025-05-25T18:53:43.201Z] Starting query planner workflow
{
  "userMessage": "hi",
  "chatHistoryLength": 1
}
-------------------------------------------
[2025-05-25T18:58:23.349Z] Starting query planner workflow
{
  "userMessage": "hi",
  "chatHistoryLength": 1
}
-------------------------------------------
[2025-05-25T18:58:27.199Z] Received query plan from Claude 2
{
  "rawPlan": "{\n  \"queries\": [],\n  \"response_expectation\": {\n    \"assemble_summary\": false\n  }\n}"
}
-------------------------------------------
[2025-05-25T18:58:27.201Z] Executing dynamic query plan
{
  "queryCount": 0,
  "databases": [],
  "queryTypes": []
}
-------------------------------------------
[2025-05-25T18:58:27.201Z] Query plan execution complete
{
  "totalQueries": 0,
  "successfulQueries": 0,
  "failedQueries": 0,
  "survivableFailures": 0,
  "warnings": 0,
  "partialFailures": 0,
  "canContinue": false
}
-------------------------------------------
[2025-05-25T18:58:27.203Z] Deck extraction filter for _metadata
{
  "hasData": false,
  "dataType": "undefined",
  "dataLength": "N/A"
}
-------------------------------------------
[2025-05-25T18:58:27.203Z] Synergy extraction filter for _metadata
{
  "hasData": false,
  "dataType": "undefined"
}
-------------------------------------------
[2025-05-25T18:58:27.204Z] Structured Data Generated
{
  "decks": {
    "count": 0,
    "preview": []
  },
  "cards": {
    "count": 0,
    "preview": []
  },
  "synergies": {
    "count": 0,
    "preview": []
  },
  "confidence": 0.1,
  "dataQuality": "low",
  "summary": "No data could be retrieved",
  "warnings": 0,
  "partialFailures": 0
}
-------------------------------------------
[2025-05-25T18:58:27.204Z] Query Result Detail: _metadata
{
  "error": null,
  "dataType": "undefined",
  "dataPreview": null
}
-------------------------------------------
[2025-05-25T18:58:27.204Z] Query planner workflow complete
{
  "queriesExecuted": 1,
  "successfulQueries": 0,
  "failedQueries": 1,
  "structuredDataKeys": [
    "rawResults",
    "warnings",
    "partialFailures",
    "confidence",
    "dataQuality",
    "summary"
  ],
  "warnings": 0,
  "partialFailures": 0,
  "canContinue": false,
  "hasUsableData": false
}
-------------------------------------------
