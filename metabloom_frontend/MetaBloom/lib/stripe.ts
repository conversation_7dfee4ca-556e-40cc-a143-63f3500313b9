import { loadStripe } from '@stripe/stripe-js';
import Stripe from 'stripe';

// Client-side Stripe instance
export const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
);

// Server-side Stripe instance
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
});

// Subscription plan configurations
export const SUBSCRIPTION_PLANS = {
  standard: {
    name: 'Standard',
    price: 10,
    priceId: 'price_1RSlBdQ4uabTTSX2FB42wYKN', // You'll need to create these in Stripe Dashboard
    yearlyPriceId: 'price_1RSlRwQ4uabTTSX2waKHXIpn',
    features: [
      'Access to MetaBloom mini and reasoning',
      'Standard voice mode',
      'Real-time data from the web with search',
      'Limited access to MetaBloom',
      'Limited access to file uploads, data analysis, and image generation',
    ],
  },
  premium: {
    name: 'Premium',
    price: 20,
    priceId: 'price_1RSlThQ4uabTTSX2vNrlcpaS', // You'll need to create these in Stripe Dashboard
    yearlyPriceId: 'price_1RSlUIQ4uabTTSX2OuWNDMoI',
    features: [
      'Access to MetaBloom mini and reasoning',
      'Standard voice mode',
      'Real-time data from the web with search',
      'Limited access to MetaBloom',
      'Limited access to file uploads, data analysis, and image generation',
      'Use custom MetaBloom',
    ],
  },
} as const;

export type SubscriptionPlan = keyof typeof SUBSCRIPTION_PLANS;
