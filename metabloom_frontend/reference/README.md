# Reference Code from Template

This directory contains code from the original template that is not actively used in the HearthForge application but is kept for reference. These files can be used as examples or starting points for future development.

## Pages

The following pages from the original template are not actively used but are kept for reference:

### AI Generator Page
- **Original Path**: `/app/(with-layout)/ai-generator/page.tsx`
- **Description**: Page for generating AI content like images, audio, etc.
- **Status**: Not actively used in HearthForge

### Custom Bots Page
- **Original Path**: `/app/(with-layout)/custom-bots/page.tsx`
- **Description**: Page for managing custom AI bots
- **Status**: Not actively used in HearthForge

### Explore Page
- **Original Path**: `/app/(with-layout)/explore/page.tsx`
- **Description**: Page for exploring content and bots
- **Status**: Not actively used in HearthForge

### Upgrade Plan Page
- **Original Path**: `/app/(with-layout)/upgrade-plan/page.tsx`
- **Description**: Page for subscription options
- **Status**: Not actively used in HearthForge

## Components

The following components from the original template are not actively used but are kept for reference:

### Chat Components
- **Original Path**: `/components/chatComponents/`
- **Components**:
  - `BotAudioClipReply.tsx`
  - `BotCodeReply.tsx`
  - `BotImageReply.tsx`
  - `BotReply.tsx`
  - `BotRetouchImageReply.tsx`
  - `BotSuggestionReply.tsx`
  - `BotTableReply.tsx`
  - `BotVideoReply.tsx`
- **Description**: Components for displaying different types of AI responses
- **Status**: Not actively used in HearthForge (except for `AiReply.tsx`, `MyReply.tsx`, and `DeckCode.tsx`)

### Modal Components
- **Original Path**: `/components/modals/`
- **Components**:
  - `AdjustPhotoModal.tsx`
  - `AudioCreationModal.tsx`
  - `BotDetailsModal.tsx`
  - `CreateNewModal.tsx`
  - `CustomDetailsModal.tsx`
  - `EditBotModal.tsx`
  - `EditYourProfile.tsx`
  - `IntegrationModal.tsx`
  - `PerformanceModal.tsx`
  - `SearchModal.tsx`
  - `ShareCodeModal.tsx`
  - `ShareImageModal.tsx`
  - `ShareLinkModal.tsx`
  - `ShareRetouchImageModal.tsx`
  - `ShareViedeoModal.tsx`
  - `SupportModal.tsx`
  - `UpgradeModal.tsx`
  - `UploadToAIQuill.tsx`
- **Description**: Various modal components for different features
- **Status**: Not actively used in HearthForge (except for `AuthModal.tsx`, `MainModal.tsx`, and `SettingsModal.tsx`)

### UI Components
- **Original Path**: `/components/ui/`
- **Components**:
  - `AdjustPhotoRange.tsx`
  - `FaqItem.tsx`
  - `InputFieldSecond.tsx`
  - `SelectDropdown.tsx`
  - `TextArea.tsx`
  - `ToggleButton.tsx`
  - `buttons/SmallButtons.tsx`
- **Description**: UI components for various features
- **Status**: Not actively used in HearthForge (except for `FormInput.tsx` and `GradientBackground.tsx`)

### Header Components
- **Original Path**: `/components/header/`
- **Components**:
  - `UpgradeModal.tsx`
  - `UserModal.tsx`
- **Description**: Components for the header
- **Status**: Partially used in HearthForge

## How to Use Reference Code

If you want to implement a feature that's similar to one in the template:

1. Check this reference directory to see if there's a component or page that does something similar
2. Copy the relevant code to your working directory
3. Modify it to fit your needs
4. Update imports and references as needed

## Note on File Organization

Instead of physically moving the files to this directory (which could break imports and require extensive refactoring), we've documented the files here for reference. The actual files remain in their original locations in the codebase.

If you want to clean up the codebase further in the future, you could:

1. Create a proper reference directory structure
2. Copy the files there
3. Remove them from the main codebase
4. Update all imports and references

This would require more extensive refactoring but would result in a cleaner codebase.
