import { NextRequest, NextResponse } from 'next/server';
import { getStripe, SUBSCRIPTION_PLANS } from '@/lib/stripe';
import { auth } from 'firebase-admin/auth';

export async function POST(request: NextRequest) {
  try {
    const { planType, isYearly, userId } = await request.json();

    if (!planType || !userId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const plan = SUBSCRIPTION_PLANS[planType as keyof typeof SUBSCRIPTION_PLANS];
    if (!plan) {
      return NextResponse.json(
        { error: 'Invalid plan type' },
        { status: 400 }
      );
    }

    // Get the appropriate price ID based on billing frequency
    const priceId = isYearly ? plan.yearlyPriceId : plan.priceId;

    // Create Stripe checkout session
    const stripe = getStripe();
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${request.nextUrl.origin}/upgrade-plan?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${request.nextUrl.origin}/upgrade-plan?canceled=true`,
      metadata: {
        userId,
        planType,
        isYearly: isYearly.toString(),
      },
      customer_email: undefined, // Will be filled by Stripe Checkout
      allow_promotion_codes: true,
    });

    return NextResponse.json({ sessionId: session.id });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
