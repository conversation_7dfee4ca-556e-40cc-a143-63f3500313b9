/**
 * Hearthstone Deck Code Utilities
 *
 * This module provides utilities for working with Hearthstone deck codes.
 * Enhanced for the Two-Claude Architecture to support comprehensive deck code processing.
 */

import { encode, decode, FormatType } from 'deckstrings';
import { ExecutableQueries } from '@/lib/types/queryTypes';

// Regex pattern for detecting deck codes
export const DECK_CODE_REGEX = /^[A-Za-z0-9+/=]{20,150}$/;

/**
 * Interface for deck code detection configuration
 */
export interface DeckCodeDetection {
  /** Primary regex pattern for deck code identification */
  regex: RegExp;

  /** Extraction steps from user message */
  extractionSteps: string[];

  /** Validation requirements */
  validation: {
    minimumLength: number;
    maximumLength: number;
    allowedCharacters: string;
    mustNotContainSpaces: boolean;
  };
}

/**
 * Interface for deck code error handling
 */
export interface DeckCodeErrorHandling {
  /** Types of errors that can occur */
  errorTypes: {
    notFound: {
      scenario: string;
      action: string;
      queryModification: string;
      userFeedback: string;
    };
    invalidFormat: {
      scenario: string;
      action: string;
      queryModification: string;
      userFeedback: string;
    };
    decodingFailure: {
      scenario: string;
      action: string;
      queryModification: string;
      userFeedback: string;
    };
    partialDeck: {
      scenario: string;
      action: string;
      queryModification: string;
      userFeedback: string;
    };
    unknownFormat: {
      scenario: string;
      action: string;
      queryModification: string;
      userFeedback: string;
    };
  };

  /** Recovery strategies */
  recoveryStrategies: {
    gracefulDegradation: string;
    informativeErrors: string;
    alternativeHelp: string;
    contextualFallback: string;
  };
}

/**
 * Standard configuration for deck code detection
 */
export const DECK_CODE_DETECTION: DeckCodeDetection = {
  regex: DECK_CODE_REGEX,
  extractionSteps: [
    "Split user message by newlines",
    "Trim whitespace from each line",
    "Test each line against deck code regex",
    "Return first valid match or null"
  ],
  validation: {
    minimumLength: 20,
    maximumLength: 150,
    allowedCharacters: "A-Za-z0-9+/=",
    mustNotContainSpaces: true
  }
};

// Map of format IDs to format names
export const FORMAT_NAMES: Record<number, string> = {
  1: 'Wild',
  2: 'Standard',
  3: 'Classic',
  4: 'Twist'
};

// Map of hero IDs to class names
export const HERO_CLASS_MAP: Record<number, string> = {
  // Death Knight
  893: 'Death Knight',
  894: 'Death Knight',
  // Demon Hunter
  56550: 'Demon Hunter',
  60224: 'Demon Hunter',
  64697: 'Demon Hunter',
  // Druid
  274: 'Druid',
  50484: 'Druid',
  // Hunter
  31: 'Hunter',
  50579: 'Hunter',
  // Mage
  637: 'Mage',
  50590: 'Mage',
  // Paladin
  671: 'Paladin',
  50583: 'Paladin',
  // Priest
  813: 'Priest',
  50587: 'Priest',
  // Rogue
  930: 'Rogue',
  50582: 'Rogue',
  // Shaman
  1066: 'Shaman',
  50585: 'Shaman',
  // Warlock
  953: 'Warlock',  // Changed from 893 to 953 to avoid duplicate key
  50586: 'Warlock',
  // Warrior
  7: 'Warrior',
  50581: 'Warrior'
};

/**
 * Interface for a decoded Hearthstone deck
 */
export interface DecodedDeck {
  format: number;
  heroes: number[];
  cards: [number, number][]; // [cardId, count]
  formatName?: string;
  className?: string;
}

/**
 * Interface for deck statistics
 */
export interface DeckStats {
  /** Mana curve (count of cards by mana cost) */
  manaCurve: {
    [manaCost: number]: number;
  };

  /** Card type distribution */
  cardTypes: {
    minions: number;
    spells: number;
    weapons: number;
    locations: number;
    heroes: number;
    other: number;
  };

  /** Rarity distribution */
  rarityDistribution: {
    common: number;
    rare: number;
    epic: number;
    legendary: number;
    free: number;
  };

  /** Keyword counts */
  keywords: {
    [keyword: string]: number;
  };

  /** Average mana cost */
  averageManaCost: number;

  /** Estimated dust cost */
  estimatedDustCost: number;
}

/**
 * Interface for processed deck data with validation information
 */
export interface ProcessedDeckData {
  /** The original deck code */
  deckCode: string;

  /** Whether the deck code is valid */
  isValid: boolean;

  /** Decoded deck information (if valid) */
  decoded?: {
    format: number;
    formatName: string;
    heroes: number[];
    className: string;
    cards: [number, number][]; // [cardId, count] pairs
    totalCards: number;
    uniqueCards: number;
    heroId: number;
  };

  /** Validation details */
  validation: {
    hasValidStructure: boolean;
    hasCards: boolean;
    hasValidFormat: boolean;
    hasValidHero: boolean;
  };

  /** Error message if invalid */
  error?: string;

  /** Deck statistics (if valid) */
  deckStats?: DeckStats;
}

/**
 * Interface for a card in a Hearthstone deck
 */
export interface DeckCard {
  id: number;
  count: number;
  name?: string;
  cost?: number;
  rarity?: string;
  type?: string;
}

/**
 * Extract a potential deck code from a message
 *
 * @param message The message to extract a deck code from
 * @returns The extracted deck code, or null if none was found
 */
export function extractDeckCode(message: string): string | null {
  // Look for deck codes in the message
  const lines = message.split('\n');

  for (const line of lines) {
    const trimmed = line.trim();
    // Check if this line matches our deck code pattern
    if (DECK_CODE_REGEX.test(trimmed)) {
      return trimmed;
    }
  }

  return null;
}

/**
 * Validate a Hearthstone deck code
 *
 * @param deckCode The deck code to validate
 * @returns Whether the deck code is valid
 */
export function validateDeckCode(deckCode: string): boolean {
  try {
    // Attempt to decode the deck code
    const decoded = decode(deckCode);

    // Basic validation - a valid deck should have:
    // - A format (heroes[0])
    // - At least some cards
    return decoded &&
           Array.isArray(decoded.heroes) &&
           decoded.heroes.length > 0 &&
           Array.isArray(decoded.cards) &&
           decoded.cards.length > 0;
  } catch (error) {
    console.error('Error validating deck code:', error);
    return false;
  }
}

/**
 * Validate and decode a Hearthstone deck code with comprehensive information
 *
 * @param deckCode The deck code to validate and decode
 * @returns Processed deck data with validation information
 */
export function validateAndDecodeDeck(deckCode: string): ProcessedDeckData {
  try {
    // Attempt decoding with deckstrings library
    const decoded = decode(deckCode);

    // Validate decoded structure
    if (!decoded ||
        !Array.isArray(decoded.heroes) ||
        decoded.heroes.length === 0 ||
        !Array.isArray(decoded.cards) ||
        decoded.cards.length === 0) {
      throw new Error('Invalid deck structure');
    }

    // Calculate deck statistics
    const totalCards = decoded.cards.reduce((sum, [, count]) => sum + count, 0);
    const uniqueCards = decoded.cards.length;

    // Enrich with human-readable information
    const formatName = FORMAT_NAMES[decoded.format] || 'Unknown';
    const heroId = decoded.heroes[0];
    const className = HERO_CLASS_MAP[heroId] || 'Unknown';

    return {
      deckCode,
      isValid: true,
      decoded: {
        format: decoded.format,
        formatName,
        heroes: decoded.heroes,
        className,
        cards: decoded.cards,      // [cardId, count] pairs
        totalCards,
        uniqueCards,
        heroId
      },
      validation: {
        hasValidStructure: true,
        hasCards: totalCards > 0,
        hasValidFormat: decoded.format >= 1 && decoded.format <= 4,
        hasValidHero: heroId in HERO_CLASS_MAP
      }
    };

  } catch (error: any) {
    console.error('Deck decoding error:', error);
    return {
      deckCode,
      isValid: false,
      error: error.message,
      validation: {
        hasValidStructure: false,
        hasCards: false,
        hasValidFormat: false,
        hasValidHero: false
      }
    };
  }
}

/**
 * Decode a Hearthstone deck code with additional information
 *
 * @param deckCode The deck code to decode
 * @returns The decoded deck with format and class information
 */
export function decodeDeckWithInfo(deckCode: string): DecodedDeck {
  try {
    // Decode the deck code
    const decoded = decode(deckCode);

    // Add format name
    const formatName = FORMAT_NAMES[decoded.format] || 'Unknown';

    // Add class name based on hero ID
    const heroId = decoded.heroes[0];
    const className = HERO_CLASS_MAP[heroId] || 'Unknown';

    return {
      ...decoded,
      formatName,
      className
    };
  } catch (error) {
    console.error('Error decoding deck code:', error);
    throw error;
  }
}

/**
 * Encode a Hearthstone deck into a deck code
 *
 * @param format The format of the deck (1 = Wild, 2 = Standard, etc.)
 * @param heroes The hero IDs for the deck
 * @param cards The cards in the deck as [cardId, count] pairs
 * @returns The encoded deck code
 */
export function encodeDeck(format: FormatType, heroes: number[], cards: [number, number][]): string {
  try {
    return encode({
      format,
      heroes,
      cards
    });
  } catch (error) {
    console.error('Error encoding deck:', error);
    throw error;
  }
}

/**
 * Get the total number of cards in a deck
 *
 * @param cards The cards in the deck as [cardId, count] pairs
 * @returns The total number of cards
 */
export function getDeckSize(cards: [number, number][]): number {
  return cards.reduce((total, [_, count]) => total + count, 0);
}

/**
 * Check if a deck is valid (has exactly 30 cards)
 *
 * @param cards The cards in the deck as [cardId, count] pairs
 * @returns Whether the deck is valid
 */
export function isDeckValid(cards: [number, number][]): boolean {
  return getDeckSize(cards) === 30;
}

/**
 * Generate database queries based on deck data
 *
 * @param processedDeck The processed deck data
 * @returns Executable queries for the deck
 */
export function generateDeckAnalysisQueries(processedDeck: ProcessedDeckData): ExecutableQueries {
  if (!processedDeck.isValid || !processedDeck.decoded) {
    return {
      primary: [],
      conditional: []
    };
  }

  const cardIds = processedDeck.decoded.cards.map(([cardId]) => cardId);

  return {
    primary: [
      {
        id: "deck_card_details",
        database: "postgres",
        query: "SELECT card_id, name, mana_cost, attack, health, text, rarity, type, class FROM cards WHERE card_id = ANY($1)",
        params: [cardIds],
        purpose: "Get complete information for all cards in the deck",
        priority: "critical"
      },
      {
        id: "deck_synergies",
        database: "graphql",
        query: `{
          cards(where: { card_id_IN: [${cardIds.join(',')}] }) {
            name
            mana_cost
            synergizesWith(options: { limit: 5 }) {
              name
              co_occurrence_count
              synergy_strength
            }
          }
        }`,
        params: [],
        purpose: "Analyze synergies between deck cards",
        priority: "important"
      }
    ],

    conditional: [
      {
        condition: "if_card_details_successful",
        id: "similar_decks",
        database: "postgres",
        query: "SELECT name, archetype, win_rate, dust_cost, deck_code FROM meta_decks WHERE format = $1 AND class = $2 ORDER BY card_similarity DESC LIMIT 5",
        params: [processedDeck.decoded.format, processedDeck.decoded.className],
        purpose: "Find similar meta decks for comparison"
      },
      {
        condition: "if_synergies_found",
        id: "archetype_analysis",
        database: "graphql",
        query: `{
          deckArchetypes(where: {
            format: "${processedDeck.decoded.formatName}",
            class: "${processedDeck.decoded.className}"
          }) {
            name
            key_cards
            win_rate
            popularity
            counters
          }
        }`,
        params: [],
        purpose: "Identify deck archetype and meta positioning"
      }
    ]
  };
}

/**
 * Generate error response queries for invalid deck codes
 *
 * @param processedDeck The processed deck data with error information
 * @returns Executable queries for error handling
 */
export function generateErrorResponseQueries(processedDeck: ProcessedDeckData): ExecutableQueries {
  return {
    primary: [
      {
        id: "deck_code_error",
        database: "postgres",
        query: "SELECT error_type, suggestion FROM deck_code_errors WHERE error_pattern LIKE $1 LIMIT 1",
        params: [`%${processedDeck.error || 'general'}%`],
        purpose: "Get helpful error information for the user",
        priority: "important"
      },
      {
        id: "popular_decks",
        database: "postgres",
        query: "SELECT name, class, win_rate, deck_code FROM meta_decks ORDER BY popularity DESC LIMIT 5",
        params: [],
        purpose: "Provide alternative deck suggestions",
        priority: "supplementary"
      }
    ],
    conditional: []
  };
}
