"use client";
import React, { useEffect, useRef, useState } from "react";
import MyReply from "@/components/chatComponents/MyReply";
import ChatBox from "@/components/ChatBox";
import { usePathname } from "next/navigation";
import AiReply from "@/components/chatComponents/AiReply";
import { Chat, useChatHandler } from "@/stores/chatList";

function CustomChat() {
  const [scroll, setScroll] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const { chatList, userQuery, updateChatList } = useChatHandler();
  const path = usePathname();
  const [currentChat, setCurrentChat] = useState<Chat>();

  const chatId = path.split("/chat/")[1];

  // Load chat history from localStorage when component mounts
  useEffect(() => {
    updateChatList();
  }, [updateChatList]);

  // Load chat data when component mounts or chatList/chatId changes
  useEffect(() => {
    // First, check if we have a temporary navigation state for this chat
    const navKey = `nav-chat-${chatId}`;
    let navChat = null;

    try {
      const navChatJson = sessionStorage.getItem(navKey);
      if (navChatJson) {
        navChat = JSON.parse(navChatJson);
        // Remove the temporary state to avoid using stale data in the future
        sessionStorage.removeItem(navKey);
      }
    } catch (error) {
      console.error("Error retrieving navigation chat state:", error);
    }

    // If we have navigation state, use it
    if (navChat) {
      setCurrentChat(navChat);
      return;
    }

    // Otherwise, find the current chat in the chatList
    const currentChatList = chatList.find(({ id }: { id: string }) => {
      return id === chatId;
    });

    // If chat exists, set it as current chat
    if (currentChatList) {
      setCurrentChat(currentChatList);
    } else {
      // If chat doesn't exist yet (e.g., during navigation),
      // create an empty chat structure to avoid errors
      setCurrentChat({
        id: chatId,
        title: "New Chat",
        messages: []
      });
    }
  }, [chatList, path, chatId]);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [userQuery, scroll]);

  return (
    <div className=" flex flex-col gap-4 h-full flex-1 overflow-auto w-full z-20 ">
      <div className="overflow-auto w-full flex-1" ref={chatContainerRef}>
        <div className={`pb-6  flex-grow  w-full max-w-[1070px] mx-auto `}>
          <div className="flex gap-3 px-6 relative z-20  w-full flex-col ">
            {currentChat &&
              currentChat.messages.map((item, idx) => {
                return (
                  <div className="flex flex-col gap-3" key={idx}>
                    {item.isUser && typeof item.text === "string" && (
                      <MyReply replyText={item.text} replyTime="3 min ago" />
                    )}

                    {!item.isUser && typeof item.text === "string" && (
                      <AiReply
                        replyText={item.text}
                        replyTime="just now"
                        isStreaming={item.isStreaming}
                      />
                    )}
                  </div>
                );
              })}
          </div>
        </div>
      </div>

      <ChatBox />
    </div>
  );
}

export default CustomChat;
