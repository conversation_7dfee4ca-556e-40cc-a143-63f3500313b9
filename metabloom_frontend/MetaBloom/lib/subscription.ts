import { db } from '@/lib/firebase';
import { doc, getDoc, setDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { getStripe } from '@/lib/stripe';

export interface UserSubscription {
  userId: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  planType: 'free' | 'standard' | 'premium';
  status: 'active' | 'canceled' | 'past_due' | 'incomplete' | 'trialing';
  currentPeriodStart?: number;
  currentPeriodEnd?: number;
  cancelAtPeriodEnd?: boolean;
  isYearly?: boolean;
  createdAt: number;
  updatedAt: number;
}

export interface UserProfile {
  userId: string;
  email: string;
  displayName?: string;
  subscription: UserSubscription;
  tokenUsage?: {
    used: number;
    limit: number;
    resetDate: number;
  };
  createdAt: number;
  updatedAt: number;
}

// Create or update user profile
export async function createUserProfile(userId: string, email: string, displayName?: string): Promise<UserProfile> {
  const userRef = doc(db, 'users', userId);
  const now = Date.now();
  
  const defaultSubscription: UserSubscription = {
    userId,
    planType: 'free',
    status: 'active',
    createdAt: now,
    updatedAt: now,
  };

  const userProfile: UserProfile = {
    userId,
    email,
    displayName,
    subscription: defaultSubscription,
    tokenUsage: {
      used: 0,
      limit: 1000, // Free tier limit
      resetDate: now + (30 * 24 * 60 * 60 * 1000), // 30 days from now
    },
    createdAt: now,
    updatedAt: now,
  };

  await setDoc(userRef, userProfile, { merge: true });
  return userProfile;
}

// Get user profile
export async function getUserProfile(userId: string): Promise<UserProfile | null> {
  try {
    const userRef = doc(db, 'users', userId);
    const userSnap = await getDoc(userRef);
    
    if (userSnap.exists()) {
      return userSnap.data() as UserProfile;
    }
    return null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
}

// Update user subscription
export async function updateUserSubscription(
  userId: string, 
  subscriptionData: Partial<UserSubscription>
): Promise<void> {
  try {
    const userRef = doc(db, 'users', userId);
    const updateData = {
      subscription: subscriptionData,
      updatedAt: Date.now(),
    };
    
    await updateDoc(userRef, updateData);
  } catch (error) {
    console.error('Error updating user subscription:', error);
    throw error;
  }
}

// Check if user has active subscription
export async function hasActiveSubscription(userId: string, planType?: string): Promise<boolean> {
  try {
    const userProfile = await getUserProfile(userId);
    
    if (!userProfile) return false;
    
    const subscription = userProfile.subscription;
    const isActive = subscription.status === 'active' || subscription.status === 'trialing';
    
    if (planType) {
      return isActive && subscription.planType === planType;
    }
    
    return isActive && subscription.planType !== 'free';
  } catch (error) {
    console.error('Error checking subscription status:', error);
    return false;
  }
}

// Get user's current plan
export async function getCurrentPlan(userId: string): Promise<string> {
  try {
    const userProfile = await getUserProfile(userId);
    return userProfile?.subscription?.planType || 'free';
  } catch (error) {
    console.error('Error getting current plan:', error);
    return 'free';
  }
}

// Sync subscription with Stripe
export async function syncSubscriptionWithStripe(userId: string): Promise<UserSubscription | null> {
  try {
    const userProfile = await getUserProfile(userId);
    if (!userProfile?.subscription?.stripeSubscriptionId) {
      return null;
    }

    const stripe = getStripe();
    const subscription = await stripe.subscriptions.retrieve(
      userProfile.subscription.stripeSubscriptionId
    );

    const updatedSubscription: Partial<UserSubscription> = {
      status: subscription.status as UserSubscription['status'],
      currentPeriodStart: subscription.current_period_start * 1000,
      currentPeriodEnd: subscription.current_period_end * 1000,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      updatedAt: Date.now(),
    };

    await updateUserSubscription(userId, updatedSubscription);
    
    return { ...userProfile.subscription, ...updatedSubscription };
  } catch (error) {
    console.error('Error syncing subscription with Stripe:', error);
    return null;
  }
}

// Cancel subscription
export async function cancelSubscription(userId: string): Promise<boolean> {
  try {
    const userProfile = await getUserProfile(userId);
    if (!userProfile?.subscription?.stripeSubscriptionId) {
      return false;
    }

    const stripe = getStripe();
    await stripe.subscriptions.update(userProfile.subscription.stripeSubscriptionId, {
      cancel_at_period_end: true,
    });

    await updateUserSubscription(userId, {
      cancelAtPeriodEnd: true,
      updatedAt: Date.now(),
    });

    return true;
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return false;
  }
}
