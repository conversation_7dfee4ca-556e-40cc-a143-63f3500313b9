import { NextRequest, NextResponse } from 'next/server';
import { createUserProfile, getUserProfile } from '@/lib/subscription';

export async function POST(request: NextRequest) {
  try {
    const { userId, email, displayName } = await request.json();

    if (!userId || !email) {
      return NextResponse.json(
        { error: 'User ID and email are required' },
        { status: 400 }
      );
    }

    // Check if user profile already exists
    const existingProfile = await getUserProfile(userId);
    
    if (existingProfile) {
      return NextResponse.json({
        success: true,
        message: 'User profile already exists',
        profile: existingProfile,
      });
    }

    // Create new user profile
    const newProfile = await createUserProfile(userId, email, displayName);

    return NextResponse.json({
      success: true,
      message: 'User profile created successfully',
      profile: newProfile,
    });
  } catch (error) {
    console.error('Error initializing user profile:', error);
    return NextResponse.json(
      { error: 'Failed to initialize user profile' },
      { status: 500 }
    );
  }
}
