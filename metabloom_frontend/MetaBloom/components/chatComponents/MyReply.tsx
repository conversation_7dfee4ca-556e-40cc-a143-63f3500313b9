import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, PiPencilSimpleLine, PiCheck } from "react-icons/pi";

type MyReplyProps = {
  replyTime: string;
  replyText: string;
};

function MyReply({ replyTime, replyText }: MyReplyProps) {
  const [copied, setCopied] = useState(false);

  // Function to copy text to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(replyText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="flex flex-col justify-end items-end gap-3 w-full">
      <p className="text-xs text-n100">{replyTime}</p>
      <div className="text-sm user-response-container p-3 bg-primaryColor/10 rounded-lg max-w-full overflow-hidden">
        <p className="dark-text whitespace-pre-wrap break-words">{replyText}</p>
      </div>
      <div className="flex justify-end items-center gap-2">
        <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full cursor-pointer">
          <PiPencilSimpleLine />
        </button>
        <button
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full cursor-pointer"
          onClick={copyToClipboard}
        >
          {copied ? <PiCheck /> : <PiCopy />}
        </button>
      </div>
    </div>
  );
}

export default MyReply;
