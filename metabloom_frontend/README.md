# HearthForge - Hearthstone Deck Building Assistant

<PERSON><PERSON><PERSON><PERSON><PERSON> is an AI-powered Hearthstone deck building assistant that helps players create competitive decks, analyze the meta, and improve their gameplay. This application uses a conversational AI interface powered by Claude 3.5 Sonnet to provide deck recommendations, card suggestions, and strategic advice.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Getting Started](#getting-started)
3. [Project Structure](#project-structure)
4. [Key Features](#key-features)
5. [AI Architecture](#ai-architecture)
6. [Database Integration](#database-integration)
7. [Authentication](#authentication)
8. [Customization](#customization)
9. [Deployment](#deployment)
10. [Logging System](#logging-system)

## Project Overview

HearthForge is built using Next.js and integrates with Anthropic's <PERSON> to provide Hearthstone players with a powerful deck building assistant. The application features a chat interface where users can:

- Generate optimized Hearthstone decks for any class or playstyle
- Analyze existing decks for strengths and weaknesses
- Get meta insights and matchup advice
- Receive deck codes that can be directly imported into Hearthstone
- Access data from multiple databases (PostgreSQL, Vector DB, Neo4j)

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Firebase account (for authentication)
- AWS account (for database access)

### Environment Setup

Create a `.env.local` file in the root directory with the following variables:

```
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_firebase_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_firebase_measurement_id

# Anthropic API Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key

# Neo4j GraphQL API Configuration
NEO4J_GRAPHQL_API_KEY=your_neo4j_graphql_api_key
```

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd MetaBloom_frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

### AWS Configuration

The application connects to several AWS services:

1. **Lambda Function**: `hearthstone-api` - Handles database queries
2. **API Gateway**: Endpoint at `https://wa6kt26wi1.execute-api.us-east-1.amazonaws.com/prod/query`
3. **RDS PostgreSQL**: Stores structured Hearthstone data
4. **Neo4j Database**: Stores graph relationships between cards and decks

These services should be properly configured with the correct environment variables in the AWS Console.

## Project Structure

The repository is organized with a clean, flat structure:

```
/MetaBloom_frontend/                # Root directory
├── README.md                       # Main documentation
├── app/                            # Next.js app directory
│   ├── (with-layout)/              # Pages that use the main layout
│   │   ├── chat/[id]/              # Individual chat page
│   │   ├── home/                   # Home page
│   │   ├── new-chat/               # New chat page
│   │   └── layout.tsx              # Layout for authenticated pages
│   ├── api/                        # API routes
│   │   ├── auth/                   # Authentication endpoints
│   │   └── chat/                   # Chat API endpoints with Two-Claude Architecture
│   └── layout.tsx                  # Root layout
├── components/                     # React components
│   ├── chatComponents/             # Chat-related components
│   ├── header/                     # Header components
│   ├── modals/                     # Modal components
│   └── ui/                         # UI components
├── lib/                            # Library code
│   ├── claude/                     # Claude AI integration
│   │   ├── orchestrator.ts         # Orchestrator model (Claude 1)
│   │   └── processor.ts            # Processor model (Claude 2)
│   ├── graphql/                    # GraphQL integration
│   │   └── queries.ts              # GraphQL queries for Neo4j
│   ├── utils/                      # Utility functions
│   │   └── claudeLogger.ts         # Logging system for Claude
│   └── hearthstoneApi.ts           # API client for Hearthstone database
├── logs/                           # Log files directory
│   └── README.md                   # Documentation for logs
├── public/                         # Static assets
│   └── images/                     # Images and logos
├── scripts/                        # Utility scripts
│   └── view-logs.js                # Script to view Claude logs
├── stores/                         # State management
│   ├── auth.ts                     # Authentication state
│   ├── chatList.ts                 # Chat history state
│   └── modal.ts                    # Modal state
└── styles/                         # CSS styles
    ├── globals.css                 # Global styles
    ├── markdown.css                # Markdown rendering styles
    └── typing-indicator.css        # Chat typing indicator styles
```

### Key Files and Directories

#### AI Integration

- `lib/claude/orchestrator.ts` - First Claude model that analyzes user intent and prepares data
- `lib/claude/processor.ts` - Second Claude model that generates the final response
- `lib/utils/claudeLogger.ts` - Logging system for the Two-Claude Architecture
- `app/api/chat/route.ts` - API route that coordinates the Two-Claude Architecture

#### Database Integration

- `lib/hearthstoneApi.ts` - Client for the Hearthstone database API
- `lib/graphql/queries.ts` - GraphQL queries for the Neo4j database

#### UI Components

- `components/ChatBox.tsx` - Chat input component
- `components/chatComponents/AiReply.tsx` - AI reply component with markdown rendering
- `components/chatComponents/DeckCode.tsx` - Special component for Hearthstone deck codes
- `components/chatComponents/MyReply.tsx` - User message component

#### Authentication

- `stores/auth.ts` - Firebase authentication state management
- `app/api/auth/` - Authentication API endpoints

## Key Features

### Chat Interface

The chat interface is the core of HearthForge, allowing users to interact with the AI assistant:

- **Location**: `components/ChatBox.tsx`
- **Features**:
  - Multi-line text input with Shift+Enter support
  - Streaming responses with stop/pause button
  - Markdown rendering for formatted responses
  - Special handling for deck codes
  - Copy button for responses and code blocks

### Markdown Rendering

AI responses are rendered with proper formatting:

- **Location**: `components/chatComponents/AiReply.tsx`
- **Features**:
  - Headings, lists, and tables
  - Code blocks with syntax highlighting
  - Special deck code component with copy button
  - Proper styling for all markdown elements

### Chat History

The application maintains a history of conversations:

- **Location**: `stores/chatList.ts`
- **Features**:
  - Persistent chat history across sessions
  - Navigation between previous conversations
  - Real-time updates during streaming responses

## AI Architecture

HearthForge uses a Two-Claude Architecture to provide high-quality responses:

### Orchestrator (Claude 1)

The first Claude model analyzes user intent and prepares data:

- **Location**: `lib/claude/orchestrator.ts`
- **Responsibilities**:
  - Analyze user messages to determine intent
  - Detect Hearthstone deck codes
  - Decide which database functions to call
  - Structure data for the Processor
  - Handle error cases and fallbacks

### Processor (Claude 2)

The second Claude model generates the final response:

- **Location**: `lib/claude/processor.ts`
- **Responsibilities**:
  - Receive structured data from the Orchestrator
  - Generate natural, conversational responses
  - Format deck recommendations and analysis
  - Present database information in a user-friendly way
  - Maintain consistent personality and tone

### Communication Flow

1. User sends a message
2. Orchestrator analyzes the message
3. Orchestrator calls database functions if needed
4. Orchestrator structures the data
5. Processor receives the structured data
6. Processor generates the final response
7. Response is streamed to the user

## Database Integration

HearthForge connects to multiple databases to provide comprehensive Hearthstone data:

### PostgreSQL Database

Stores structured Hearthstone data:

- **Access**: Via AWS Lambda function
- **Data Stored**:
  - Card information
  - Deck statistics
  - Meta data and win rates
  - Class information

### Vector Database

Enables semantic search for Hearthstone content:

- **Access**: Via AWS Lambda function
- **Features**:
  - Semantic search for cards by description
  - Finding similar decks
  - Archetype classification

### Neo4j Graph Database

Stores relationships between Hearthstone entities:

- **Access**: Via GraphQL API
- **Data Stored**:
  - Card synergies
  - Deck archetypes
  - Card generation relationships
  - Matchup data

### API Client

The application uses a unified API client to access all databases:

- **Location**: `lib/hearthstoneApi.ts`
- **Features**:
  - Cached API calls to reduce redundant requests
  - Error handling with retry logic
  - Support for PostgreSQL, Vector, and Neo4j queries

## Authentication

HearthForge uses Firebase for authentication:

- **Location**: `stores/auth.ts`
- **Features**:
  - Email/password authentication
  - Passwordless email link authentication
  - Persistent login state
  - Protected routes for authenticated users

## Customization

### Logos and Branding

The application uses custom logos and branding:

- **Location**: `public/images/`
- **Files**:
  - `MetaBloom_logo.png`: Main logo used in the chat interface
  - `MetaBloom_largesize.png`: Larger version of the logo
  - `favicon.ico`: Browser favicon

### Colors and Styling

The application uses a custom color scheme:

- **Primary Color**: Orange (#FF5C00)
- **Background**: Dark mode with gradient accents
- **CSS Location**: `styles/globals.css`

## Deployment

The application can be deployed to various platforms:

### Build for Production

```bash
npm run build
```

### Start Production Server

```bash
npm start
```

### Deploy to Vercel

The easiest way to deploy is using Vercel:

1. Push your code to a GitHub repository
2. Import the project in Vercel
3. Configure environment variables
4. Deploy

## Logging System

HearthForge includes a comprehensive logging system for the Two-Claude Architecture:

### Log Files

- **Location**: `logs/` directory
- **Files**:
  - `orchestrator.log`: Logs from the Orchestrator model
  - `processor.log`: Logs from the Processor model
  - `claude_communication.log`: Combined logs showing the full communication flow

### Viewing Logs

Use the provided script to view logs:

```bash
# View all logs
node scripts/view-logs.js

# View only Orchestrator logs
node scripts/view-logs.js orchestrator

# View only Processor logs
node scripts/view-logs.js processor
```

### Real-time Log Monitoring

You can also watch logs in real-time:

```bash
# Watch all logs
tail -f logs/claude_communication.log

# Watch Orchestrator logs
tail -f logs/orchestrator.log

# Watch Processor logs
tail -f logs/processor.log
```

### What's in the Logs

- User messages and their analysis
- Database function calls and results
- Structured data passed between models
- Error messages and debugging information
- Complete conversation flow

## License

This project is licensed under the MIT License - see the LICENSE file for details.
