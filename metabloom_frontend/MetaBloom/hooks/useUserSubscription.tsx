"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/stores/auth';
import { useSubscription } from '@/stores/subscription';

interface SubscriptionData {
  hasActiveSubscription: boolean;
  currentPlan: string;
  subscription: {
    planType: string;
    status: string;
    currentPeriodEnd?: number;
    cancelAtPeriodEnd?: boolean;
    isYearly?: boolean;
  } | null;
  tokenUsage?: {
    used: number;
    limit: number;
    resetDate: number;
  };
}

export function useUserSubscription() {
  const { user, isAuthenticated } = useAuth();
  const { setSubscription } = useSubscription();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);

  const fetchSubscriptionStatus = async () => {
    if (!user || !isAuthenticated) {
      setSubscriptionData(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/subscription/status?userId=${user.uid}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch subscription status');
      }

      const data: SubscriptionData = await response.json();
      setSubscriptionData(data);

      // Update global subscription store
      setSubscription({
        hasActiveSubscription: data.hasActiveSubscription,
        subscriptionPlan: data.currentPlan,
        subscriptionStatus: data.subscription?.status || 'inactive',
      });

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch subscription');
      console.error('Error fetching subscription status:', err);
    } finally {
      setLoading(false);
    }
  };

  // Auto-fetch when user changes
  useEffect(() => {
    fetchSubscriptionStatus();
  }, [user, isAuthenticated]);

  // Refresh subscription data
  const refreshSubscription = () => {
    fetchSubscriptionStatus();
  };

  // Check if user has specific plan
  const hasPlan = (planType: string): boolean => {
    return subscriptionData?.hasActiveSubscription && 
           subscriptionData?.currentPlan === planType;
  };

  // Check if user has any premium plan
  const hasPremiumAccess = (): boolean => {
    return subscriptionData?.hasActiveSubscription && 
           subscriptionData?.currentPlan !== 'free';
  };

  // Get plan display name
  const getPlanDisplayName = (): string => {
    if (!subscriptionData) return 'Free';
    
    const plan = subscriptionData.currentPlan;
    switch (plan) {
      case 'standard':
        return 'Standard';
      case 'premium':
        return 'Premium';
      default:
        return 'Free';
    }
  };

  // Check if subscription is ending soon (within 7 days)
  const isEndingSoon = (): boolean => {
    if (!subscriptionData?.subscription?.currentPeriodEnd) return false;
    
    const endDate = new Date(subscriptionData.subscription.currentPeriodEnd);
    const now = new Date();
    const daysUntilEnd = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    return daysUntilEnd <= 7 && daysUntilEnd > 0;
  };

  return {
    subscriptionData,
    loading,
    error,
    refreshSubscription,
    hasPlan,
    hasPremiumAccess,
    getPlanDisplayName,
    isEndingSoon,
    // Convenience getters
    currentPlan: subscriptionData?.currentPlan || 'free',
    hasActiveSubscription: subscriptionData?.hasActiveSubscription || false,
    tokenUsage: subscriptionData?.tokenUsage,
  };
}
