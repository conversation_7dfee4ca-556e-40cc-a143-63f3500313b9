"use client";

import { useState } from 'react';
import { useStripe } from '@stripe/react-stripe-js';
import { useAuth } from '@/stores/auth';

export function useStripeSubscription() {
  const stripe = useStripe();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createCheckoutSession = async (planType: string, isYearly: boolean = false) => {
    if (!stripe || !user) {
      setError('Stripe not loaded or user not authenticated');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planType,
          isYearly,
          userId: user.uid,
        }),
      });

      const { sessionId, error: apiError } = await response.json();

      if (apiError) {
        setError(apiError);
        return;
      }

      // Redirect to Stripe Checkout
      const { error: stripeError } = await stripe.redirectToCheckout({
        sessionId,
      });

      if (stripeError) {
        setError(stripeError.message || 'Failed to redirect to checkout');
      }
    } catch (err) {
      setError('Failed to create checkout session');
      console.error('Subscription error:', err);
    } finally {
      setLoading(false);
    }
  };

  return {
    createCheckoutSession,
    loading,
    error,
  };
}
