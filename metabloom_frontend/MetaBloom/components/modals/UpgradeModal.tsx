import Image from "next/image";
import React, { useState } from "react";
import {
  PiCheckCircle,
  PiLockKey,
  PiX,
} from "react-icons/pi";
import upgradeImg from "@/public/images/upgrade-header.png";
import { useMainModal } from "@/stores/modal";
import { useStripeSubscription } from "@/hooks/useSubscription";
import { useAuth } from "@/stores/auth";

// This will be dynamically generated based on plan

function UpgradeModal() {
  const [selectedPrice, setSelectedPrice] = useState(0);
  const { modalClose, modalParams } = useMainModal();
  const { createCheckoutSession, loading, error } = useStripeSubscription();
  const { isAuthenticated } = useAuth();

  // Extract plan details from modal parameters
  const planName = modalParams.planName || 'Premium';
  const planPrice = modalParams.planPrice || 20;
  const planType = modalParams.planType || 'premium';

  // Generate dynamic pricing data based on plan
  // Apply 20% discount for yearly plans
  const yearlyPrice = Math.round(planPrice * 12 * 0.8);
  const pricingData = [
    {
      id: 1,
      title: "Pay monthly",
      price: `${planPrice}/month`,
    },
    {
      id: 2,
      title: "Pay yearly",
      price: `${yearlyPrice}/year`,
    },
  ];

  const handleSubscribe = async () => {
    if (!isAuthenticated) {
      alert('Please sign in to subscribe');
      return;
    }

    const isYearly = selectedPrice === 1;
    await createCheckoutSession(planType, isYearly);
  };
  return (
    <div className="">
      <div className="relative">
        <Image src={upgradeImg} alt="" className="w-full" />
        <div
          onClick={modalClose}
          className="absolute top-4 right-4  rounded-full p-1 sm:p-2 flex justify-center items-center bg-white cursor-pointer dark:bg-n0"
        >
          <PiX className="text-errorColor text-xl sm:text-2xl" />
        </div>
      </div>
      <div className="px-4 sm:px-[60px] pb-6 sm:pb-10  ">
        <div className="bg-white dark:bg-n0 relative z-10 rounded-xl">
          <div className="bg-secondaryColor/5 border border-secondaryColor/30 rounded-xl p-3 sm:py-5 sm:px-6 -mt-12">
            <p className="text-xl sm:text-2xl font-semibold">{planName}</p>
            <p className="text-n700 pt-2 max-sm:text-sm dark:text-n30">
              ${planPrice}.00/month
            </p>
          </div>
          <div className="pt-3 flex justify-start items-center gap-2 sm:gap-3 max-[430px]:flex-col">
            {pricingData.map(({ id, title, price }, idx) => (
              <div
                className={`p-3 sm:p-5 rounded-xl flex-1 bg-primaryColor/5 border relative w-full ${
                  selectedPrice === idx
                    ? " border-primaryColor"
                    : "border-primaryColor/30"
                }`}
                key={id}
                onClick={() => setSelectedPrice(idx)}
              >
                <div
                  className={`absolute top-2 right-2 text-primaryColor ${
                    selectedPrice === idx ? "" : "opacity-0"
                  }`}
                >
                  <PiCheckCircle className="text-2xl" />
                </div>
                <p className="text-sm font-medium pb-2">{title}</p>
                <div className="flex justify-between items-center ">
                  <p className="font-semibold text-n700 dark:text-n30">
                    {price}
                  </p>
                  {idx === 1 && (
                    <p className="text-successColor bg-successColor/5 border border-successColor/30 rounded-md px-2 text-sm">
                      Save 20%
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-start items-center gap-2 pt-6">
            <PiLockKey className="text-xl text-primaryColor" />
            <p className="text-sm">Secure payment powered by Stripe</p>
          </div>
          <div className="pt-6 sm:pt-10 flex flex-col justify-end items-end text-end">
            <p className="text-n700 text-2xl font-semibold pt-2 dark:text-n30">
              Billed Now: ${selectedPrice === 0 ? planPrice : yearlyPrice}
            </p>
            <p className="text-sm py-5 w-full sm:w-[450px]">
              By clicking &quot;Start {planName} plan&quot;, you agree to be
              charged ${selectedPrice === 0 ? planPrice : yearlyPrice} every {selectedPrice === 0 ? 'month' : 'year'}, unless you cancel.
            </p>
            <button
              onClick={handleSubscribe}
              disabled={loading}
              className="text-white bg-primaryColor rounded-full py-3 px-6 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Processing...' : `Start ${planName} Plan`}
            </button>
            {error && (
              <p className="text-errorColor text-sm mt-2">{error}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default UpgradeModal;
