# Stripe Integration Setup Guide

This guide will help you complete the Stripe integration for MetaBloom subscriptions.

## Prerequisites

- Stripe test account (already configured with your keys)
- Access to Stripe Dashboard

## Required Setup Steps

### 1. Create Products and Prices in Stripe Dashboard

You need to create the following products and prices in your Stripe Dashboard:

#### Standard Plan
1. Go to Stripe Dashboard > Products
2. Create a new product named "Standard Plan"
3. Create two prices for this product:
   - **Monthly**: $10.00 USD, recurring monthly
   - **Yearly**: $96.00 USD, recurring yearly (20% discount)
4. Copy the price IDs and update `lib/stripe.ts`:
   ```typescript
   standard: {
     priceId: 'price_1234567890abcdef', // Replace with actual monthly price ID
     yearlyPriceId: 'price_0987654321fedcba', // Replace with actual yearly price ID
   }
   ```

#### Premium Plan
1. Create a new product named "Premium Plan"
2. Create two prices for this product:
   - **Monthly**: $20.00 USD, recurring monthly
   - **Yearly**: $192.00 USD, recurring yearly (20% discount)
3. Copy the price IDs and update `lib/stripe.ts`:
   ```typescript
   premium: {
     priceId: 'price_abcdef1234567890', // Replace with actual monthly price ID
     yearlyPriceId: 'price_fedcba0987654321', // Replace with actual yearly price ID
   }
   ```

### 2. Set Up Webhook Endpoint

1. Go to Stripe Dashboard > Developers > Webhooks
2. Click "Add endpoint"
3. Set the endpoint URL to: `https://yourdomain.com/api/stripe/webhook`
4. Select the following events to listen for:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
5. Copy the webhook signing secret and update your `.env.local`:
   ```
   STRIPE_WEBHOOK_SECRET=whsec_your_actual_webhook_secret_here
   ```

### 3. Test the Integration

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Navigate to the upgrade plan page
3. Click on either "Upgrade Plus" (Standard) or "Upgrade Pro" (Premium)
4. The modal should open with the correct pricing
5. Click "Start [Plan] Plan" to initiate Stripe Checkout
6. Use Stripe test card numbers:
   - Success: `4242 4242 4242 4242`
   - Decline: `4000 0000 0000 0002`

### 4. Environment Variables

Make sure your `.env.local` file contains:

```env
# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RSkrtQ4uabTTSX2CZYJAhzBwMhgmWcz1WfDanKQnO2vNDZwHAW8qNQBMxichda0tLvmCUULzVx0RPomKJw48Le400Rst5JHpy
STRIPE_SECRET_KEY=sk_test_51RSkrtQ4uabTTSX2z1pfxDO56nYUgRZgcll0NF97xX8bzK3PnVYeKbh1o8ztN8qRfrHRTBLiDy3utPlIUIWtVfno00NhkajG1S
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

## Features Implemented

✅ **Environment Variables**: Stripe keys securely stored in `.env.local`
✅ **Stripe Configuration**: Client and server-side Stripe instances
✅ **Checkout Sessions**: API route to create Stripe checkout sessions
✅ **Webhook Handler**: Process Stripe events (subscription lifecycle)
✅ **UI Integration**: Updated UpgradeModal with Stripe checkout
✅ **Plan Differentiation**: Separate flows for Standard ($10) and Premium ($20)
✅ **Loading States**: UI feedback during checkout process
✅ **Error Handling**: Proper error messages and validation

## Next Steps

1. **Complete Stripe Dashboard setup** (products, prices, webhooks)
2. **Test the full flow** with test cards
3. **Implement subscription management** (cancel, upgrade/downgrade)
4. **Add subscription status checking** to restrict features
5. **Deploy and configure production webhooks**

## Security Notes

- Never commit your secret keys to version control
- Use test keys for development
- Set up proper webhook signature verification
- Implement proper user authentication checks

## Troubleshooting

- **Checkout not opening**: Check browser console for Stripe errors
- **Webhook failures**: Verify webhook secret and endpoint URL
- **Price ID errors**: Ensure price IDs in `lib/stripe.ts` match Stripe Dashboard
- **Authentication issues**: Verify user is logged in before checkout
